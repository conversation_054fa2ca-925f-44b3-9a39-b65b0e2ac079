/**
 * Language Store
 * 
 * Manages application language state with session persistence.
 * Supports Thai (TH) and English (EN) languages.
 * 
 * Features:
 * - Reactive language switching
 * - Session storage persistence
 * - Default language fallback
 * - Language validation
 */

import { writable, derived } from 'svelte/store';

// Supported languages
export const SUPPORTED_LANGUAGES = {
  TH: 'TH',
  EN: 'EN'
};

// Default language
export const DEFAULT_LANGUAGE = SUPPORTED_LANGUAGES.TH;

// Session storage key
const LANGUAGE_STORAGE_KEY = 'insurance_portal_language';

/**
 * Get language from session storage
 * @returns {string} Stored language or default
 */
function getStoredLanguage() {
  if (typeof window === 'undefined') return DEFAULT_LANGUAGE;
  
  try {
    const stored = sessionStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (stored && Object.values(SUPPORTED_LANGUAGES).includes(stored)) {
      return stored;
    }
  } catch (error) {
    console.warn('Error reading language from session storage:', error);
  }
  
  return DEFAULT_LANGUAGE;
}

/**
 * Save language to session storage
 * @param {string} language - Language to save
 */
function saveLanguageToStorage(language) {
  if (typeof window === 'undefined') return;
  
  try {
    sessionStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    console.warn('Error saving language to session storage:', error);
  }
}

/**
 * Validate language code
 * @param {string} language - Language to validate
 * @returns {boolean} Whether language is supported
 */
export function isValidLanguage(language) {
  return Object.values(SUPPORTED_LANGUAGES).includes(language);
}

// Create language store with initial value from storage
export const currentLanguageStore = writable(getStoredLanguage());

// Derived stores for convenience
export const isThaiLanguage = derived(
  currentLanguageStore,
  $language => $language === SUPPORTED_LANGUAGES.TH
);

export const isEnglishLanguage = derived(
  currentLanguageStore,
  $language => $language === SUPPORTED_LANGUAGES.EN
);

export const languageDisplayName = derived(
  currentLanguageStore,
  $language => {
    switch ($language) {
      case SUPPORTED_LANGUAGES.TH:
        return 'ไทย';
      case SUPPORTED_LANGUAGES.EN:
        return 'English';
      default:
        return 'ไทย';
    }
  }
);

/**
 * Set current language
 * @param {string} language - Language to set
 */
export function setLanguage(language) {
  if (!isValidLanguage(language)) {
    console.warn(`Invalid language: ${language}. Using default: ${DEFAULT_LANGUAGE}`);
    language = DEFAULT_LANGUAGE;
  }
  
  currentLanguageStore.set(language);
  saveLanguageToStorage(language);
  
  console.log(`Language changed to: ${language}`);
}

/**
 * Toggle between Thai and English
 */
export function toggleLanguage() {
  currentLanguageStore.update(current => {
    const newLanguage = current === SUPPORTED_LANGUAGES.TH 
      ? SUPPORTED_LANGUAGES.EN 
      : SUPPORTED_LANGUAGES.TH;
    
    saveLanguageToStorage(newLanguage);
    console.log(`Language toggled to: ${newLanguage}`);
    
    return newLanguage;
  });
}

/**
 * Get current language value (non-reactive)
 * @returns {string} Current language
 */
export function getCurrentLanguage() {
  let language;
  currentLanguageStore.subscribe(value => language = value)();
  return language;
}

/**
 * Initialize language store (call on app startup)
 */
export function initializeLanguageStore() {
  const storedLanguage = getStoredLanguage();
  currentLanguageStore.set(storedLanguage);
  console.log(`Language store initialized with: ${storedLanguage}`);
}
