<!--
  PolicyListEN.svelte

  English version of the comprehensive responsive insurance policy list component 
  that displays customer policies in an enhanced card layout with detailed information.

  Features:
  - Responsive CSS Grid layout optimized for comprehensive policy information
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges and information sections
  - Professional card design with comprehensive policy details
  - English language labels with English code/comments
  - Currency and date formatting for English locale
  - Graceful handling of missing data fields

  Enhanced Information Display:
  - Member information (name, contact details, demographics)
  - Policy numbers (policy, certificate, insurance card, staff number)
  - Coverage details (coverage amount, premium with Thai Baht formatting)
  - Plan information (effective dates, plan code)
  - Company information (insurer, company details)
  - Additional metadata (citizen ID, language, citizenship)

  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Small (640px - 768px): 1 card per row
  - Medium (768px - 1024px): 2 cards per row
  - Large (1024px - 1280px): 2 cards per row
  - XL (1280px - 1536px): 3 cards per row
  - 2XL (> 1536px): 3 cards per row

  Usage:
  <PolicyListEN />

  Future Enhancement: Accept policies as props
  <PolicyListEN {policies} />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";
  import { policiesStore, loadPolicies } from "./stores/dataStore.js";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedCitizenStore,
    selectedInsurerStore,
  } from "./stores/memberStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";
  import TwoStepMemberSelectorEN from "./components/TwoStepMemberSelectorEN.svelte";
  import { getApiCitizenId } from "./utils/memberData.js";
  import {
    t,
    getPolicyTypeTranslation,
    formatCurrencyByLanguage,
    formatDateByLanguage,
  } from "./utils/i18n.js";
  import { SUPPORTED_LANGUAGES } from "./stores/languageStore.js";

  const dispatch = createEventDispatcher();

  // Language constant for this component
  const LANGUAGE = SUPPORTED_LANGUAGES.EN;

  // Reactive store subscriptions
  $: policies = $policiesStore.data || [];
  $: loading = $policiesStore.loading;
  $: error = $policiesStore.error;
  $: selectedMember = $selectedMemberStore;
  $: memberDisplayName = $selectedMemberDisplayName;
  $: selectedCitizen = $selectedCitizenStore;
  $: selectedInsurer = $selectedInsurerStore;

  // Check if both citizen and insurer are selected
  $: bothSelected = selectedCitizen && selectedInsurer;

  // Policy type icons mapping - updated for insurance plans
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Medical: "🏥",
    Business: "🏢",
    General: "📄",
    Basic: "📋",
    Premium: "⭐",
    Executive: "💼",
  };

  // Status color mapping
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
    Suspended: "bg-orange-100 text-orange-800 border-orange-200",
  };

  // Get policy icon based on plan name
  function getPolicyIcon(planName) {
    if (!planName) return typeIcons.General;

    const planLower = planName.toLowerCase();
    if (planLower.includes("auto") || planLower.includes("car"))
      return typeIcons.Auto;
    if (planLower.includes("home") || planLower.includes("house"))
      return typeIcons.Home;
    if (planLower.includes("life")) return typeIcons.Life;
    if (planLower.includes("health") || planLower.includes("medical"))
      return typeIcons.Health;
    if (planLower.includes("business")) return typeIcons.Business;
    if (planLower.includes("premium")) return typeIcons.Premium;
    if (planLower.includes("executive")) return typeIcons.Executive;
    if (planLower.includes("basic")) return typeIcons.Basic;

    return typeIcons.General;
  }

  // Get policy type display name
  function getPolicyTypeDisplay(planName) {
    if (!planName) return t("policyTypes.General", LANGUAGE);

    const planLower = planName.toLowerCase();
    if (planLower.includes("auto") || planLower.includes("car"))
      return getPolicyTypeTranslation("Auto", LANGUAGE);
    if (planLower.includes("home") || planLower.includes("house"))
      return getPolicyTypeTranslation("Home", LANGUAGE);
    if (planLower.includes("life"))
      return getPolicyTypeTranslation("Life", LANGUAGE);
    if (planLower.includes("health") || planLower.includes("medical"))
      return getPolicyTypeTranslation("Health", LANGUAGE);
    if (planLower.includes("business"))
      return getPolicyTypeTranslation("Business", LANGUAGE);
    if (planLower.includes("premium"))
      return getPolicyTypeTranslation("Premium", LANGUAGE);
    if (planLower.includes("executive"))
      return getPolicyTypeTranslation("Executive", LANGUAGE);
    if (planLower.includes("basic"))
      return getPolicyTypeTranslation("Basic", LANGUAGE);

    return getPolicyTypeTranslation("General", LANGUAGE);
  }

  // Format currency for display
  function formatCurrency(amount) {
    return formatCurrencyByLanguage(amount, LANGUAGE);
  }

  // Format date for display
  function formatDate(date) {
    return formatDateByLanguage(date, LANGUAGE);
  }

  // Handle policy card click to navigate to detail page
  function handlePolicyClick(memberCode) {
    dispatch("navigate", {
      page: "policy-detail",
      memberCode: memberCode,
    });
  }

  // Load policies data based on selected member
  async function loadPoliciesData() {
    if (!selectedMember) {
      console.warn("No member selected, cannot load policies");
      policiesStore.setError(new Error(t("selectMemberFirst", LANGUAGE)));
      return;
    }

    if (!selectedMember?.insurerCode || !selectedMember?.citizenID) {
      console.warn("Selected member missing required data for API call");
      policiesStore.setError(new Error(t("incompleteMemberData", LANGUAGE)));
      return;
    }

    try {
      // Convert citizen code to API citizen ID for API call
      const apiCitizenId = getApiCitizenId(selectedMember.citizenID);

      const searchParams = {
        INSURER_CODE: selectedMember.insurerCode,
        CITIZEN_ID: apiCitizenId,
      };

      console.log(`Making API call with params:`, {
        insurerCode: selectedMember.insurerCode,
        citizenId: apiCitizenId,
        originalCitizenCode: selectedMember.citizenID,
      });

      await loadPolicies(searchParams);
      console.log(
        `Policies loaded successfully for member ${selectedMember?.memberCode}:`,
        $policiesStore.data?.length || 0,
      );
    } catch (error) {
      console.error("Failed to load policies:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPoliciesData();
  }

  // Load policies when member selection changes
  $: if (selectedMember && bothSelected) {
    loadPoliciesData();
  }

  // Initialize component
  onMount(() => {
    // Initial load will be triggered by reactive statement when member is available
  });
</script>

<main
  class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8"
  aria-label="Insurance Policy List"
>
  <div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <header class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-6">
        {t("pageTitle", LANGUAGE)}
      </h1>

      <!-- Two-Step Member Selector -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <TwoStepMemberSelectorEN
          compact={false}
          showLabels={true}
          horizontal={true}
        />
      </div>

      <!-- Selection Status -->
      {#if selectedMember}
        <!-- Member selected status can be shown here if needed -->
      {:else if selectedCitizen && selectedInsurer}
        <p class="text-gray-600">{t("loading", LANGUAGE)}</p>
      {/if}
    </header>

    <!-- No Selection State -->
    {#if !bothSelected}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">👤</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          {t("selectMemberFirst", LANGUAGE)}
        </h2>
        <p class="text-gray-600 mb-6">
          {t("selectBothCitizenAndInsurer", LANGUAGE)}
        </p>
      </div>

      <!-- Loading State -->
    {:else if loading}
      <LoadingStates
        variant="cards"
        count={6}
        message={t("loadingPolicies", LANGUAGE)}
      />

      <!-- Error State -->
    {:else if error}
      <ErrorStates
        variant="api"
        {error}
        on:retry={handleRetry}
        message={t("errorLoadingData", LANGUAGE)}
      />

      <!-- Empty State -->
    {:else if policies.length === 0}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">📄</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">No Policies Found</h2>
        <p class="text-gray-600 mb-6">
          No insurance policies found for this member
        </p>
        <button
          class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          on:click={handleRetry}
          aria-label="Refresh policies"
        >
          <svg
            class="mr-2 w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          {t("retry", LANGUAGE)}
        </button>
      </div>

      <!-- Policies Grid -->
    {:else}
      <!-- Member Information -->
      <section class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {t("memberInfo", LANGUAGE)}
        </h2>
        <div class="grid gap-4 sm:grid-cols-2">
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              Name (Thai)
            </div>
            <p class="text-gray-900">
              {selectedMember?.titleTH || ""}
              {selectedMember?.nameTH || ""}
              {selectedMember?.surnameTH || ""}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              Name (English)
            </div>
            <p class="text-gray-900">
              {selectedMember?.titleEN || ""}
              {selectedMember?.nameEN || ""}
              {selectedMember?.surnameEN || ""}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              {t("citizenId", LANGUAGE)}
            </div>
            <p class="text-gray-900 font-mono">
              {selectedMember?.citizenID || "Not specified"}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">Birth Date</div>
            <p class="text-gray-900">
              {formatDate(policies[0]?.BirthDate)}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">Gender</div>
            <p class="text-gray-900">
              {policies[0].Gender === "M"
                ? "Male"
                : policies[0].Gender === "F"
                  ? "Female"
                  : "Not specified"}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              {t("citizenship", LANGUAGE)}
            </div>
            <p class="text-gray-900">
              {policies[0].Citizenship === "Thai"
                ? "Thai"
                : policies[0].Citizenship}
            </p>
          </div>
          {#if policies[0].CompanyName && policies[0].CompanyName !== policies[0].InsurerName}
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">
                Partner Company
              </div>
              <p class="text-gray-900">
                {policies[0].CompanyNameEN || policies[0].CompanyName}
              </p>
            </div>
          {/if}
        </div>
      </section>

      <p class="pt-10 text-gray-600">
        {t("policiesOf", LANGUAGE)}
        <!-- <span class="font-semibold text-gray-800">{memberDisplayName}</span> -->
        <span class="text-gray-900">
          {selectedMember.titleEN || ""}
          {selectedMember.nameEN || ""}
          {selectedMember.surnameEN || ""}
        </span>
        {#if selectedMember?.memberCode}
          <span class="text-sm text-gray-500 ml-2"
            >({t("memberCode", LANGUAGE)}: {selectedMember.memberCode})</span
          >
        {/if}
      <section
        class="grid gap-6 py-6
             grid-cols-1
             sm:grid-cols-1
             md:grid-cols-3
             lg:grid-cols-3
             xl:grid-cols-4
             2xl:grid-cols-4"
        aria-label="Policy cards grid"
      >
        {#each policies as policy (policy.MemberCode || policy.PolicyNo)}
          <button
            class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[480px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
            aria-labelledby="policy-{policy.MemberCode}-title"
            on:click={() => handlePolicyClick(policy.MemberCode)}
          >
            <!-- Policy Header -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-3">
                <h2
                  id="policy-{policy.MemberCode}-title"
                  class="text-lg font-semibold text-gray-900 flex items-center gap-2"
                >
                  <span class="text-2xl" aria-hidden="true"
                    >{getPolicyIcon(policy.PlanName)}</span
                  >
                  {getPolicyTypeDisplay(policy.PlanName)}
                </h2>
                <div class="flex flex-row items-end gap-1">
                  <span
                    class="px-3 py-1 rounded-full text-xs font-medium border
                       {statusColors[policy.MemberStatus] ||
                      statusColors.Active}"
                    aria-label="Member status: {policy.MemberStatus}"
                  >
                    {policy.MemberStatus}
                  </span>
                  {#if policy.VIP === "Y"}
                    <span
                      class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200"
                    >
                      VIP
                    </span>
                  {/if}
                </div>
              </div>

              <!-- Policy Numbers -->
              <div class="space-y-1 mb-3">
                <p
                  class="text-sm text-gray-600 flex justify-between items-center"
                >
                  <span class="text-sm text-gray-500"
                    >{t("policyNumber", LANGUAGE)}</span
                  >
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.PolicyNo}
                  </span>
                </p>
                <p
                  class="text-sm text-gray-600 flex justify-between items-center"
                >
                  <span class="text-sm text-gray-500"
                    >{t("certificateNumber", LANGUAGE)}</span
                  >
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.CertificateNo}
                  </span>
                </p>
                {#if policy.InsurerCardNo}
                  <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                  >
                    <span class="text-sm text-gray-500"
                      >{t("insuranceCardNumber", LANGUAGE)}</span
                    >
                    <span class="text-sm font-medium text-gray-900 text-right">
                      {policy.InsurerCardNo}
                    </span>
                  </p>
                {/if}
                {#if policy.StaffNo}
                  <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                  >
                    <span class="text-sm text-gray-500"
                      >{t("staffNumber", LANGUAGE)}</span
                    >
                    <span class="text-sm font-medium text-gray-900 text-right">
                      {policy.StaffNo}
                    </span>
                  </p>
                {/if}
              </div>
            </div>

            <!-- Policy Details -->
            <div class="space-y-4 mb-4 flex-grow">
              <!-- Member & Card Information -->
              <div class="bg-gray-50 rounded-lg p-3 space-y-2">
                <h3
                  class="text-xs font-semibold text-gray-700 uppercase tracking-wide"
                >
                  {t("memberInfo", LANGUAGE)}
                </h3>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span class="text-gray-500"
                      >{t("memberType", LANGUAGE)}</span
                    >
                    <div class="font-medium text-gray-900">
                      {policy.MemberType}
                    </div>
                  </div>
                  <div>
                    <span class="text-gray-500">{t("cardType", LANGUAGE)}</span>
                    <div class="font-medium text-gray-900">
                      {policy.CardType}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Coverage & Premium Information -->
              {#if policy.CoverageAmount || policy.Premium}
                <div class="bg-blue-50 rounded-lg p-3 space-y-2">
                  <h3
                    class="text-xs font-semibold text-blue-700 uppercase tracking-wide"
                  >
                    Coverage
                  </h3>
                  <div class="space-y-2 text-xs">
                    {#if policy.CoverageAmount}
                      <div class="flex justify-between items-center">
                        <span class="text-blue-600"
                          >{t("coverageAmount", LANGUAGE)}</span
                        >
                        <span class="font-semibold text-blue-900">
                          {formatCurrency(policy.CoverageAmount)}
                        </span>
                      </div>
                    {/if}
                    {#if policy.Premium}
                      <div class="flex justify-between items-center">
                        <span class="text-blue-600"
                          >{t("premium", LANGUAGE)}</span
                        >
                        <span class="font-semibold text-blue-900">
                          {formatCurrency(policy.Premium)}
                        </span>
                      </div>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Plan & Date Information -->
              <div class="bg-green-50 rounded-lg p-3 space-y-2">
                <h3
                  class="text-xs font-semibold text-green-700 uppercase tracking-wide"
                >
                  Coverage Period
                </h3>
                <div class="space-y-2 text-xs">
                  <div class="flex justify-between items-center">
                    <span class="text-green-600"
                      >{t("effectiveDate", LANGUAGE)}</span
                    >
                    <span class="font-medium text-green-900">
                      {formatDate(policy.PlanEffFrom)}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-green-600"
                      >{t("expiryDate", LANGUAGE)}</span
                    >
                    <span class="font-medium text-green-900">
                      {formatDate(policy.PlanEffTo)}
                    </span>
                  </div>
                  {#if policy.PlanCode}
                    <div class="flex justify-between items-center">
                      <span class="text-green-600"
                        >{t("planCode", LANGUAGE)}:</span
                      >
                      <span class="font-medium text-green-900">
                        {policy.PlanCode}
                      </span>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Company Information -->
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-500"
                    >{t("insurer", LANGUAGE)}</span
                  >
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.InsurerNameEN || policy.InsurerName}
                  </span>
                </div>
              </div>
            </div>

            <!-- Plan Description -->
            <div class="border-t border-gray-100 pt-4">
              <div class="space-y-2">
                <p class="text-xs text-gray-600 leading-relaxed line-clamp-2">
                  {t("planCode", LANGUAGE)}:
                  <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                    {policy.PlanCode}
                  </span>
                </p>
                <p class="text-xs text-gray-600 leading-relaxed line-clamp-2">
                  Plan Name:
                  <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                    {policy.PlanName}
                  </span>
                </p>
              </div>
            </div>
          </button>
        {/each}
      </section>
    {/if}
  </div>
</main>
