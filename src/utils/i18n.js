/**
 * Internationalization Utilities
 * 
 * Provides text translations and language-specific formatting
 * for the Insurance Portal application.
 * 
 * Features:
 * - Text translation mappings
 * - Language-specific date/currency formatting
 * - Fallback handling for missing translations
 */

import { SUPPORTED_LANGUAGES } from '../stores/languageStore.js';

// Text translations for PolicyList page
export const translations = {
  // Page titles and headers
  pageTitle: {
    [SUPPORTED_LANGUAGES.TH]: 'กรมธรรม์ประกันภัย',
    [SUPPORTED_LANGUAGES.EN]: 'Insurance Policies'
  },

  // Member selection
  selectMember: {
    [SUPPORTED_LANGUAGES.TH]: 'เลือกสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Select Member'
  },
  selectInsurer: {
    [SUPPORTED_LANGUAGES.TH]: 'เลือกบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Select Insurer'
  },
  noInsurerSelected: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่ได้เลือกบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'No insurer selected'
  },
  noInsurerFound: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่พบบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'No insurers found'
  },

  // Policy information
  policiesOf: {
    [SUPPORTED_LANGUAGES.TH]: 'กรมธรรม์ของ',
    [SUPPORTED_LANGUAGES.EN]: 'Policies of'
  },
  memberCode: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Code'
  },

  // Policy details
  policyNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่กรมธรรม์',
    [SUPPORTED_LANGUAGES.EN]: 'Policy Number'
  },
  certificateNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่ใบรับรอง',
    [SUPPORTED_LANGUAGES.EN]: 'Certificate Number'
  },
  insuranceCardNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขที่บัตรประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Insurance Card Number'
  },
  staffNumber: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสพนักงาน',
    [SUPPORTED_LANGUAGES.EN]: 'Staff Number'
  },
  coverageAmount: {
    [SUPPORTED_LANGUAGES.TH]: 'วงเงินคุ้มครอง',
    [SUPPORTED_LANGUAGES.EN]: 'Coverage Amount'
  },
  premium: {
    [SUPPORTED_LANGUAGES.TH]: 'เบี้ยประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Premium'
  },
  effectiveDate: {
    [SUPPORTED_LANGUAGES.TH]: 'วันที่มีผล',
    [SUPPORTED_LANGUAGES.EN]: 'Effective Date'
  },
  expiryDate: {
    [SUPPORTED_LANGUAGES.TH]: 'วันที่หมดอายุ',
    [SUPPORTED_LANGUAGES.EN]: 'Expiry Date'
  },
  planCode: {
    [SUPPORTED_LANGUAGES.TH]: 'รหัสแผน',
    [SUPPORTED_LANGUAGES.EN]: 'Plan Code'
  },

  // Member information
  memberInfo: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Information'
  },
  citizenId: {
    [SUPPORTED_LANGUAGES.TH]: 'เลขบัตรประชาชน',
    [SUPPORTED_LANGUAGES.EN]: 'Citizen ID'
  },
  memberType: {
    [SUPPORTED_LANGUAGES.TH]: 'ประเภทสมาชิก',
    [SUPPORTED_LANGUAGES.EN]: 'Member Type'
  },
  cardType: {
    [SUPPORTED_LANGUAGES.TH]: 'ประเภทบัตร',
    [SUPPORTED_LANGUAGES.EN]: 'Card Type'
  },
  language: {
    [SUPPORTED_LANGUAGES.TH]: 'ภาษา',
    [SUPPORTED_LANGUAGES.EN]: 'Language'
  },
  citizenship: {
    [SUPPORTED_LANGUAGES.TH]: 'สัญชาติ',
    [SUPPORTED_LANGUAGES.EN]: 'Citizenship'
  },

  // Company information
  companyInfo: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลบริษัท',
    [SUPPORTED_LANGUAGES.EN]: 'Company Information'
  },
  insurer: {
    [SUPPORTED_LANGUAGES.TH]: 'บริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Insurer'
  },
  companyName: {
    [SUPPORTED_LANGUAGES.TH]: 'ชื่อบริษัท',
    [SUPPORTED_LANGUAGES.EN]: 'Company Name'
  },

  // Loading and error states
  loading: {
    [SUPPORTED_LANGUAGES.TH]: 'กำลังโหลด...',
    [SUPPORTED_LANGUAGES.EN]: 'Loading...'
  },
  loadingPolicies: {
    [SUPPORTED_LANGUAGES.TH]: 'กำลังโหลดข้อมูลกรมธรรม์...',
    [SUPPORTED_LANGUAGES.EN]: 'Loading policies...'
  },
  noDataAvailable: {
    [SUPPORTED_LANGUAGES.TH]: 'ไม่มีข้อมูล',
    [SUPPORTED_LANGUAGES.EN]: 'No data available'
  },
  errorLoadingData: {
    [SUPPORTED_LANGUAGES.TH]: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    [SUPPORTED_LANGUAGES.EN]: 'Error loading data'
  },
  retry: {
    [SUPPORTED_LANGUAGES.TH]: 'ลองใหม่',
    [SUPPORTED_LANGUAGES.EN]: 'Retry'
  },

  // Selection prompts
  selectMemberFirst: {
    [SUPPORTED_LANGUAGES.TH]: 'กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์',
    [SUPPORTED_LANGUAGES.EN]: 'Please select a member to view policies'
  },
  incompleteMemberData: {
    [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลสมาชิกไม่ครบถ้วน',
    [SUPPORTED_LANGUAGES.EN]: 'Incomplete member data'
  },
  selectBothCitizenAndInsurer: {
    [SUPPORTED_LANGUAGES.TH]: 'กรุณาเลือกทั้งสมาชิกและบริษัทประกัน',
    [SUPPORTED_LANGUAGES.EN]: 'Please select both citizen and insurer'
  },

  // Policy types (for display)
  policyTypes: {
    Auto: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันรถยนต์',
      [SUPPORTED_LANGUAGES.EN]: 'Auto Insurance'
    },
    Home: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันบ้าน',
      [SUPPORTED_LANGUAGES.EN]: 'Home Insurance'
    },
    Life: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันชีวิต',
      [SUPPORTED_LANGUAGES.EN]: 'Life Insurance'
    },
    Health: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันสุขภาพ',
      [SUPPORTED_LANGUAGES.EN]: 'Health Insurance'
    },
    Medical: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันการรักษาพยาบาล',
      [SUPPORTED_LANGUAGES.EN]: 'Medical Insurance'
    },
    Business: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันธุรกิจ',
      [SUPPORTED_LANGUAGES.EN]: 'Business Insurance'
    },
    General: {
      [SUPPORTED_LANGUAGES.TH]: 'ประกันทั่วไป',
      [SUPPORTED_LANGUAGES.EN]: 'General Insurance'
    },
    Basic: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนพื้นฐาน',
      [SUPPORTED_LANGUAGES.EN]: 'Basic Plan'
    },
    Premium: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนพรีเมียม',
      [SUPPORTED_LANGUAGES.EN]: 'Premium Plan'
    },
    Executive: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนผู้บริหาร',
      [SUPPORTED_LANGUAGES.EN]: 'Executive Plan'
    }
  },

  // PolicyDetail specific translations
  policyDetail: {
    // Page title and navigation
    pageTitle: {
      [SUPPORTED_LANGUAGES.TH]: 'รายละเอียดกรมธรรม์ประกันภัย',
      [SUPPORTED_LANGUAGES.EN]: 'Insurance Policy Details'
    },
    backToList: {
      [SUPPORTED_LANGUAGES.TH]: 'กลับไปรายการกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Back to Policy List'
    },
    viewPolicyList: {
      [SUPPORTED_LANGUAGES.TH]: 'ดูรายการกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'View Policy List'
    },

    // Member selection states
    noMemberSelected: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่ได้เลือกสมาชิก',
      [SUPPORTED_LANGUAGES.EN]: 'No Member Selected'
    },
    selectMemberPrompt: {
      [SUPPORTED_LANGUAGES.TH]: 'กรุณาเลือกสมาชิกจากเมนูด้านบนเพื่อดูรายละเอียดกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Please select a member from the menu above to view policy details'
    },

    // Loading states
    loadingDetails: {
      [SUPPORTED_LANGUAGES.TH]: 'กำลังโหลดรายละเอียดกรมธรรม์...',
      [SUPPORTED_LANGUAGES.EN]: 'Loading policy details...'
    },

    // Error states
    errorLoadingDetails: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่สามารถโหลดรายละเอียดกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง',
      [SUPPORTED_LANGUAGES.EN]: 'Unable to load policy details. Please try again.'
    },
    policyNotFound: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่พบข้อมูลกรมธรรม์ที่ร้องขอ',
      [SUPPORTED_LANGUAGES.EN]: 'Requested policy information not found'
    },

    // Member information
    member: {
      [SUPPORTED_LANGUAGES.TH]: 'สมาชิก',
      [SUPPORTED_LANGUAGES.EN]: 'Member'
    },
    memberStatus: {
      [SUPPORTED_LANGUAGES.TH]: 'สถานะสมาชิก',
      [SUPPORTED_LANGUAGES.EN]: 'Member Status'
    },
    memberCode: {
      [SUPPORTED_LANGUAGES.TH]: 'รหัสสมาชิก',
      [SUPPORTED_LANGUAGES.EN]: 'Member Code'
    },

    // Policy information section
    policyInfo: {
      [SUPPORTED_LANGUAGES.TH]: 'ข้อมูลกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Policy Information'
    },
    policyNo: {
      [SUPPORTED_LANGUAGES.TH]: 'หมายเลขกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Policy Number'
    },
    certificateNo: {
      [SUPPORTED_LANGUAGES.TH]: 'หมายเลขใบรับรอง',
      [SUPPORTED_LANGUAGES.EN]: 'Certificate Number'
    },
    planName: {
      [SUPPORTED_LANGUAGES.TH]: 'แผนประกันภัย',
      [SUPPORTED_LANGUAGES.EN]: 'Insurance Plan'
    },
    effectiveFrom: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่มีผลบังคับใช้',
      [SUPPORTED_LANGUAGES.EN]: 'Effective From'
    },
    effectiveTo: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่สิ้นสุด',
      [SUPPORTED_LANGUAGES.EN]: 'Effective To'
    },
    insurerName: {
      [SUPPORTED_LANGUAGES.TH]: 'บริษัทประกันภัย',
      [SUPPORTED_LANGUAGES.EN]: 'Insurance Company'
    },
    contractCompany: {
      [SUPPORTED_LANGUAGES.TH]: 'บริษัทคู่สัญญา',
      [SUPPORTED_LANGUAGES.EN]: 'Contract Company'
    },

    // Benefits sections
    mainBenefits: {
      [SUPPORTED_LANGUAGES.TH]: 'ความคุ้มครองหลัก',
      [SUPPORTED_LANGUAGES.EN]: 'Main Benefits'
    },
    detailedBenefits: {
      [SUPPORTED_LANGUAGES.TH]: 'รายละเอียดความคุ้มครอง',
      [SUPPORTED_LANGUAGES.EN]: 'Detailed Coverage'
    },
    remaining: {
      [SUPPORTED_LANGUAGES.TH]: 'คงเหลือ',
      [SUPPORTED_LANGUAGES.EN]: 'Remaining'
    },
    limitPerVisit: {
      [SUPPORTED_LANGUAGES.TH]: 'วงเงินต่อครั้ง',
      [SUPPORTED_LANGUAGES.EN]: 'Limit per Visit'
    },
    limitPerYear: {
      [SUPPORTED_LANGUAGES.TH]: 'วงเงินต่อปี',
      [SUPPORTED_LANGUAGES.EN]: 'Annual Limit'
    },
    remainingPerYear: {
      [SUPPORTED_LANGUAGES.TH]: 'คงเหลือต่อปี',
      [SUPPORTED_LANGUAGES.EN]: 'Remaining per Year'
    },

    // Contract conditions
    contractConditions: {
      [SUPPORTED_LANGUAGES.TH]: 'เงื่อนไขสัญญา',
      [SUPPORTED_LANGUAGES.EN]: 'Contract Conditions'
    },
    exclusion: {
      [SUPPORTED_LANGUAGES.TH]: 'ข้อยกเว้น',
      [SUPPORTED_LANGUAGES.EN]: 'Exclusion'
    },
    waitingPeriod: {
      [SUPPORTED_LANGUAGES.TH]: 'ระยะเวลารอคุ้มครอง',
      [SUPPORTED_LANGUAGES.EN]: 'Waiting Period'
    },
    effective: {
      [SUPPORTED_LANGUAGES.TH]: 'มีผล',
      [SUPPORTED_LANGUAGES.EN]: 'Effective'
    },
    notEffective: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่มีผล',
      [SUPPORTED_LANGUAGES.EN]: 'Not Effective'
    },
    startDate: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่เริ่มต้น',
      [SUPPORTED_LANGUAGES.EN]: 'Start Date'
    },
    endDate: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่สิ้นสุด',
      [SUPPORTED_LANGUAGES.EN]: 'End Date'
    },
    remarks: {
      [SUPPORTED_LANGUAGES.TH]: 'หมายเหตุ',
      [SUPPORTED_LANGUAGES.EN]: 'Remarks'
    },

    // Member conditions
    memberConditions: {
      [SUPPORTED_LANGUAGES.TH]: 'เงื่อนไขเฉพาะสมาชิก',
      [SUPPORTED_LANGUAGES.EN]: 'Member-Specific Conditions'
    },
    medicalHistory: {
      [SUPPORTED_LANGUAGES.TH]: 'ประวัติการรักษา',
      [SUPPORTED_LANGUAGES.EN]: 'Medical History'
    },
    medication: {
      [SUPPORTED_LANGUAGES.TH]: 'การใช้ยา',
      [SUPPORTED_LANGUAGES.EN]: 'Medication'
    },
    monitor: {
      [SUPPORTED_LANGUAGES.TH]: 'ต้องติดตาม',
      [SUPPORTED_LANGUAGES.EN]: 'Monitor'
    },
    alert: {
      [SUPPORTED_LANGUAGES.TH]: 'เฝ้าระวัง',
      [SUPPORTED_LANGUAGES.EN]: 'Alert'
    },

    // Claims history
    claimsHistory: {
      [SUPPORTED_LANGUAGES.TH]: 'ประวัติการเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'Claims History'
    },
    totalClaims: {
      [SUPPORTED_LANGUAGES.TH]: 'ทั้งหมด',
      [SUPPORTED_LANGUAGES.EN]: 'Total'
    },
    claimsCount: {
      [SUPPORTED_LANGUAGES.TH]: 'รายการ',
      [SUPPORTED_LANGUAGES.EN]: 'claims'
    },
    noClaimsFound: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่พบประวัติการเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'No Claims Found'
    },
    noClaimsDescription: {
      [SUPPORTED_LANGUAGES.TH]: 'ยังไม่มีประวัติการเคลมสำหรับสมาชิกนี้ หรือข้อมูลอาจยังไม่ได้รับการอัปเดต',
      [SUPPORTED_LANGUAGES.EN]: 'No claim history found for this member, or data may not be updated yet'
    },

    // Claim details
    claimNumber: {
      [SUPPORTED_LANGUAGES.TH]: 'เลขที่',
      [SUPPORTED_LANGUAGES.EN]: 'Claim No.'
    },
    claimStatus: {
      [SUPPORTED_LANGUAGES.TH]: 'สถานะเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'Claim Status'
    },
    claimType: {
      [SUPPORTED_LANGUAGES.TH]: 'ประเภทเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'Claim Type'
    },
    paidAmount: {
      [SUPPORTED_LANGUAGES.TH]: 'จำนวนที่จ่าย',
      [SUPPORTED_LANGUAGES.EN]: 'Paid Amount'
    },
    claimedAmount: {
      [SUPPORTED_LANGUAGES.TH]: 'จำนวนที่เรียกร้อง',
      [SUPPORTED_LANGUAGES.EN]: 'Claimed Amount'
    },
    difference: {
      [SUPPORTED_LANGUAGES.TH]: 'ส่วนต่าง',
      [SUPPORTED_LANGUAGES.EN]: 'Difference'
    },
    serviceDate: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่เข้ารับบริการ',
      [SUPPORTED_LANGUAGES.EN]: 'Service Date'
    },
    claimDate: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่ยื่นเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'Claim Date'
    },
    settlementDate: {
      [SUPPORTED_LANGUAGES.TH]: 'วันที่จ่ายเงิน',
      [SUPPORTED_LANGUAGES.EN]: 'Settlement Date'
    },
    provider: {
      [SUPPORTED_LANGUAGES.TH]: 'สถานพยาบาล/ผู้ให้บริการ',
      [SUPPORTED_LANGUAGES.EN]: 'Healthcare Provider'
    },

    // Claim status translations
    claimStatuses: {
      Paid: {
        [SUPPORTED_LANGUAGES.TH]: 'จ่ายแล้ว',
        [SUPPORTED_LANGUAGES.EN]: 'Paid'
      },
      Approved: {
        [SUPPORTED_LANGUAGES.TH]: 'อนุมัติแล้ว',
        [SUPPORTED_LANGUAGES.EN]: 'Approved'
      },
      Authorized: {
        [SUPPORTED_LANGUAGES.TH]: 'อนุญาตแล้ว',
        [SUPPORTED_LANGUAGES.EN]: 'Authorized'
      },
      Pending: {
        [SUPPORTED_LANGUAGES.TH]: 'รอดำเนินการ',
        [SUPPORTED_LANGUAGES.EN]: 'Pending'
      },
      'Pending For Approval': {
        [SUPPORTED_LANGUAGES.TH]: 'รอการอนุมัติ',
        [SUPPORTED_LANGUAGES.EN]: 'Pending For Approval'
      },
      Open: {
        [SUPPORTED_LANGUAGES.TH]: 'เปิดอยู่',
        [SUPPORTED_LANGUAGES.EN]: 'Open'
      },
      Rejected: {
        [SUPPORTED_LANGUAGES.TH]: 'ปฏิเสธ',
        [SUPPORTED_LANGUAGES.EN]: 'Rejected'
      }
    },

    // Claim type translations
    claimTypes: {
      AUTO: {
        [SUPPORTED_LANGUAGES.TH]: 'รถยนต์',
        [SUPPORTED_LANGUAGES.EN]: 'Auto'
      },
      HEALTH: {
        [SUPPORTED_LANGUAGES.TH]: 'สุขภาพ',
        [SUPPORTED_LANGUAGES.EN]: 'Health'
      },
      LIFE: {
        [SUPPORTED_LANGUAGES.TH]: 'ชีวิต',
        [SUPPORTED_LANGUAGES.EN]: 'Life'
      },
      PROPERTY: {
        [SUPPORTED_LANGUAGES.TH]: 'ทรัพย์สิน',
        [SUPPORTED_LANGUAGES.EN]: 'Property'
      },
      TRAVEL: {
        [SUPPORTED_LANGUAGES.TH]: 'การเดินทาง',
        [SUPPORTED_LANGUAGES.EN]: 'Travel'
      },
      Repair: {
        [SUPPORTED_LANGUAGES.TH]: 'ซ่อมแซม',
        [SUPPORTED_LANGUAGES.EN]: 'Repair'
      },
      Medical: {
        [SUPPORTED_LANGUAGES.TH]: 'การรักษา',
        [SUPPORTED_LANGUAGES.EN]: 'Medical'
      },
      Accident: {
        [SUPPORTED_LANGUAGES.TH]: 'อุบัติเหตุ',
        [SUPPORTED_LANGUAGES.EN]: 'Accident'
      }
    },

    // Action buttons and alerts
    editPolicy: {
      [SUPPORTED_LANGUAGES.TH]: 'แก้ไขข้อมูลกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Edit Policy'
    },
    renewPolicy: {
      [SUPPORTED_LANGUAGES.TH]: 'ต่ออายุกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Renew Policy'
    },
    cancelPolicy: {
      [SUPPORTED_LANGUAGES.TH]: 'ยกเลิกกรมธรรม์',
      [SUPPORTED_LANGUAGES.EN]: 'Cancel Policy'
    },
    downloadDocument: {
      [SUPPORTED_LANGUAGES.TH]: 'ดาวน์โหลดเอกสาร',
      [SUPPORTED_LANGUAGES.EN]: 'Download Document'
    },
    viewClaim: {
      [SUPPORTED_LANGUAGES.TH]: 'ดูรายละเอียดเคลม',
      [SUPPORTED_LANGUAGES.EN]: 'View Claim Details'
    },

    // Alert messages
    editPolicyAlert: {
      [SUPPORTED_LANGUAGES.TH]: 'ฟังก์ชันแก้ไขข้อมูลกรมธรรม์จะถูกพัฒนาในอนาคต',
      [SUPPORTED_LANGUAGES.EN]: 'Policy editing functionality will be developed in the future'
    },
    renewPolicyAlert: {
      [SUPPORTED_LANGUAGES.TH]: 'ฟังก์ชันต่ออายุกรมธรรม์จะถูกพัฒนาในอนาคต',
      [SUPPORTED_LANGUAGES.EN]: 'Policy renewal functionality will be developed in the future'
    },
    cancelPolicyConfirm: {
      [SUPPORTED_LANGUAGES.TH]: 'คุณแน่ใจหรือไม่ที่จะยกเลิกกรมธรรม์นี้?',
      [SUPPORTED_LANGUAGES.EN]: 'Are you sure you want to cancel this policy?'
    },
    cancelPolicyAlert: {
      [SUPPORTED_LANGUAGES.TH]: 'ฟังก์ชันยกเลิกกรมธรรม์จะถูกพัฒนาในอนาคต',
      [SUPPORTED_LANGUAGES.EN]: 'Policy cancellation functionality will be developed in the future'
    },
    downloadDocumentAlert: {
      [SUPPORTED_LANGUAGES.TH]: 'ฟังก์ชันดาวน์โหลดเอกสาร {docId} จะถูกพัฒนาในอนาคต',
      [SUPPORTED_LANGUAGES.EN]: 'Document download functionality for {docId} will be developed in the future'
    },
    viewClaimAlert: {
      [SUPPORTED_LANGUAGES.TH]: 'ฟังก์ชันดูรายละเอียดเคลม {claimId} จะถูกพัฒนาในอนาคต',
      [SUPPORTED_LANGUAGES.EN]: 'Claim details functionality for {claimId} will be developed in the future'
    },

    // Common terms
    notSpecified: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่ระบุ',
      [SUPPORTED_LANGUAGES.EN]: 'Not specified'
    },
    noDetails: {
      [SUPPORTED_LANGUAGES.TH]: 'ไม่ระบุรายละเอียด',
      [SUPPORTED_LANGUAGES.EN]: 'No details specified'
    }
  }
};

/**
 * Get translated text
 * @param {string} key - Translation key
 * @param {string} language - Language code
 * @param {string} fallback - Fallback text if translation not found
 * @returns {string} Translated text
 */
export function t(key, language = SUPPORTED_LANGUAGES.TH, fallback = '') {
  try {
    const translation = translations[key];
    if (!translation) {
      console.warn(`Translation key not found: ${key}`);
      return fallback || key;
    }

    return translation[language] || translation[SUPPORTED_LANGUAGES.TH] || fallback || key;
  } catch (error) {
    console.warn(`Error getting translation for key: ${key}`, error);
    return fallback || key;
  }
}

/**
 * Get policy type display name
 * @param {string} policyType - Policy type key
 * @param {string} language - Language code
 * @returns {string} Localized policy type name
 */
export function getPolicyTypeTranslation(policyType, language = SUPPORTED_LANGUAGES.TH) {
  if (!policyType) return '';

  const policyTranslations = translations.policyTypes[policyType];
  if (!policyTranslations) {
    return policyType; // Return original if no translation found
  }

  return policyTranslations[language] || policyTranslations[SUPPORTED_LANGUAGES.TH] || policyType;
}

/**
 * Format currency based on language
 * @param {number} amount - Amount to format
 * @param {string} language - Language code
 * @returns {string} Formatted currency
 */
export function formatCurrencyByLanguage(amount, language = SUPPORTED_LANGUAGES.TH) {
  if (isNaN(amount)) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';

  const locale = language === SUPPORTED_LANGUAGES.EN ? 'en-US' : 'th-TH';
  const currency = 'THB';

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Format date based on language
 * @param {string|Date} date - Date to format
 * @param {string} language - Language code
 * @returns {string} Formatted date
 */
export function formatDateByLanguage(date, language = SUPPORTED_LANGUAGES.TH) {
  if (!date) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
    }

    if (language === SUPPORTED_LANGUAGES.EN) {
      // English format: Month DD, YYYY
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      return dateObj.toLocaleDateString('en-US', options);
    } else {
      // Thai format with Buddhist Era (BE)
      const day = dateObj.getDate().toString().padStart(2, '0');
      const monthName = dateObj.toLocaleDateString('th-TH', { month: 'long' });
      const yearBE = dateObj.getFullYear() + 543;
      return `${day} ${monthName} ${yearBE}`;
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
  }
}

/**
 * Format short date based on language
 * @param {string|Date} date - Date to format
 * @param {string} language - Language code
 * @returns {string} Formatted short date
 */
export function formatShortDateByLanguage(date, language = SUPPORTED_LANGUAGES.TH) {
  if (!date) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
    }

    if (language === SUPPORTED_LANGUAGES.EN) {
      // English format: MMM DD, YYYY
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      return dateObj.toLocaleDateString('en-US', options);
    } else {
      // Thai format with Buddhist Era (BE)
      const day = dateObj.getDate().toString().padStart(2, '0');
      const monthName = dateObj.toLocaleDateString('th-TH', { month: 'short' });
      const yearBE = dateObj.getFullYear() + 543;
      return `${day} ${monthName} ${yearBE}`;
    }
  } catch (error) {
    console.warn('Error formatting short date:', error);
    return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
  }
}

/**
 * Get PolicyDetail translation
 * @param {string} key - Translation key within policyDetail
 * @param {string} language - Language code
 * @param {string} fallback - Fallback text if translation not found
 * @returns {string} Translated text
 */
export function tPolicyDetail(key, language = SUPPORTED_LANGUAGES.TH, fallback = '') {
  try {
    const translation = translations.policyDetail[key];
    if (!translation) {
      console.warn(`PolicyDetail translation key not found: ${key}`);
      return fallback || key;
    }

    return translation[language] || translation[SUPPORTED_LANGUAGES.TH] || fallback || key;
  } catch (error) {
    console.warn(`Error getting PolicyDetail translation for key: ${key}`, error);
    return fallback || key;
  }
}

/**
 * Get claim status translation
 * @param {string} status - Claim status
 * @param {string} language - Language code
 * @returns {string} Translated claim status
 */
export function getClaimStatusTranslation(status, language = SUPPORTED_LANGUAGES.TH) {
  if (!status) return language === SUPPORTED_LANGUAGES.EN ? 'Unknown' : 'ไม่ทราบ';

  const statusTranslations = translations.policyDetail.claimStatuses[status];
  if (!statusTranslations) {
    return status; // Return original if no translation found
  }

  return statusTranslations[language] || statusTranslations[SUPPORTED_LANGUAGES.TH] || status;
}

/**
 * Get claim type translation
 * @param {string} type - Claim type
 * @param {string} language - Language code
 * @returns {string} Translated claim type
 */
export function getClaimTypeTranslation(type, language = SUPPORTED_LANGUAGES.TH) {
  if (!type) return language === SUPPORTED_LANGUAGES.EN ? 'Not specified' : 'ไม่ระบุ';

  const upperType = type.toUpperCase();
  const typeTranslations = translations.policyDetail.claimTypes[upperType] || translations.policyDetail.claimTypes[type];

  if (!typeTranslations) {
    return type; // Return original if no translation found
  }

  return typeTranslations[language] || typeTranslations[SUPPORTED_LANGUAGES.TH] || type;
}
