<!--
  PolicyDetailEN.svelte

  English version of the comprehensive policy detail component that displays detailed policy information.

  Features:
  - Comprehensive policy information display
  - Policy status and key dates section
  - Action buttons for policy management
  - Related documents and claims sections
  - Responsive design with Tailwind CSS utility classes
  - Semantic HTML structure with ARIA labels
  - Back navigation functionality
  - Integration with member selection functionality
  - English language interface

  Usage:
  <PolicyDetailEN on:navigate />
-->

<script>
  import { createEventDispatcher } from "svelte";
  import { policyDetailStore, loadPolicyDetail } from "./stores/dataStore.js";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
  } from "./stores/memberStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";
  import api from "./api/index.js";
  import { getApiCitizenId } from "./utils/memberData.js";
  import { SUPPORTED_LANGUAGES } from "./stores/languageStore.js";
  import {
    tPolicyDetail,
    formatCurrencyByLanguage,
    formatDateByLanguage,
    formatShortDateByLanguage,
    getClaimStatusTranslation,
    getClaimTypeTranslation,
  } from "./utils/i18n.js";

  // Set language to English for this component
  const LANGUAGE = SUPPORTED_LANGUAGES.EN;

  const dispatch = createEventDispatcher();

  // Policy list data from PolicyList API
  let policyListData = null;

  // Reactive store subscriptions
  $: policyDetail = $policyDetailStore.data;
  $: loading = $policyDetailStore.loading;
  $: error = $policyDetailStore.error;
  $: selectedMember = $selectedMemberStore;
  $: memberDisplayName = $selectedMemberDisplayName;

  // Navigation handler
  function handleBackNavigation() {
    dispatch("navigate", { page: "policy-list" });
  }

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Inactive: "bg-gray-100 text-gray-800 border-gray-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Claim status color classes for enhanced display
  const claimStatusColors = {
    Paid: "bg-green-100 text-green-800 border-green-200",
    Approved: "bg-blue-100 text-blue-800 border-blue-200",
    Authorized: "bg-blue-100 text-blue-800 border-blue-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    "Pending For Approval": "bg-yellow-100 text-yellow-800 border-yellow-200",
    Open: "bg-orange-100 text-orange-800 border-orange-200",
    Rejected: "bg-red-100 text-red-800 border-red-200",
  };

  // Claim type color classes
  const claimTypeColors = {
    AUTO: "bg-purple-100 text-purple-800 border-purple-200",
    HEALTH: "bg-teal-100 text-teal-800 border-teal-200",
    LIFE: "bg-indigo-100 text-indigo-800 border-indigo-200",
    PROPERTY: "bg-amber-100 text-amber-800 border-amber-200",
    TRAVEL: "bg-cyan-100 text-cyan-800 border-cyan-200",
    DEFAULT: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Utility functions
  function formatCurrency(amount) {
    if (!amount) return tPolicyDetail("notSpecified", LANGUAGE);
    return formatCurrencyByLanguage(amount, LANGUAGE);
  }

  function formatDate(dateString) {
    if (!dateString) return tPolicyDetail("notSpecified", LANGUAGE);
    return formatDateByLanguage(dateString, LANGUAGE);
  }

  function formatShortDate(dateString) {
    if (!dateString) return tPolicyDetail("notSpecified", LANGUAGE);
    return formatShortDateByLanguage(dateString, LANGUAGE);
  }

  // Enhanced claim utility functions
  function getClaimStatusColor(status) {
    return claimStatusColors[status] || claimStatusColors.Pending;
  }

  function getClaimTypeColor(type) {
    const upperType = type?.toUpperCase();
    return claimTypeColors[upperType] || claimTypeColors.DEFAULT;
  }

  function formatClaimStatus(status) {
    return getClaimStatusTranslation(status, LANGUAGE);
  }

  function formatClaimType(type) {
    return getClaimTypeTranslation(type, LANGUAGE);
  }

  function formatDateTime(dateString) {
    if (!dateString) return tPolicyDetail("notSpecified", LANGUAGE);
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // Load policy detail data using selected member's memberCode
  async function loadPolicyDetailData() {
    if (!selectedMember?.memberCode) return;

    try {
      await loadPolicyDetail(selectedMember.memberCode);
      console.log(
        "Policy detail loaded successfully for member:",
        selectedMember.memberCode,
      );
    } catch (error) {
      console.error("Failed to load policy detail:", error);
    }
  }

  // Load policy list data for the policy information section
  async function loadPolicyListData() {
    if (!selectedMember?.insurerCode || !selectedMember?.citizenID) return;

    try {
      // Convert citizen code to API citizen ID for API call
      const apiCitizenId = getApiCitizenId(selectedMember.citizenID);

      // Use the existing API client to fetch policy data
      const result = await api.policies.searchByCitizenId(
        apiCitizenId,
        selectedMember.insurerCode,
      );

      if (result.success && result.data && result.data.length > 0) {
        // Use the first policy from the response
        const policyData = result.data[0];
        policyListData = policyData;
        console.log(
          "Policy list data loaded successfully for member:",
          selectedMember.memberCode,
          policyData,
        );
      } else {
        console.warn(
          "No policy data found for member:",
          selectedMember.memberCode,
        );
        policyListData = null;
      }
    } catch (error) {
      console.error("Failed to load policy list data:", error);
      policyListData = null;
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPolicyDetailData();
    await loadPolicyListData();
  }

  // Watch for selected member changes and load data
  $: if (selectedMember?.memberCode) {
    loadPolicyDetailData();
  }

  // Watch for selected member changes and load policy list data
  $: if (selectedMember?.insurerCode && selectedMember?.citizenID) {
    loadPolicyListData();
  }

  // Use policy detail from API and extract sections
  $: selectedPolicy = policyDetail;
  $: policyDetailList = policyDetail?.ListPolicyDetail || [];
  $: benefitList = policyDetail?.BenefitList || [];
  $: contractConditions = policyDetail?.ListContractCondition || [];
  $: memberConditions = policyDetail?.ListMemberCondition || [];
  $: claimHistory = policyDetail?.ListClaimHistory || [];

  // Action handlers
  function handleEditPolicy() {
    alert(tPolicyDetail("editPolicyAlert", LANGUAGE));
  }

  function handleRenewPolicy() {
    alert(tPolicyDetail("renewPolicyAlert", LANGUAGE));
  }

  function handleCancelPolicy() {
    if (confirm(tPolicyDetail("cancelPolicyConfirm", LANGUAGE))) {
      alert(tPolicyDetail("cancelPolicyAlert", LANGUAGE));
    }
  }

  function handleDownloadDocument(docId) {
    const message = tPolicyDetail("downloadDocumentAlert", LANGUAGE).replace(
      "{docId}",
      docId,
    );
    alert(message);
  }

  function handleViewClaim(claimId) {
    const message = tPolicyDetail("viewClaimAlert", LANGUAGE).replace(
      "{claimId}",
      claimId,
    );
    alert(message);
  }
</script>

{#if !selectedMember}
  <!-- No Member Selected State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("backToList", LANGUAGE)}
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {tPolicyDetail("backToList", LANGUAGE)}
      </button>
    </div>

    <div class="max-w-4xl mx-auto text-center">
      <div class="text-8xl mb-6" aria-hidden="true">👤</div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        {tPolicyDetail("noMemberSelected", LANGUAGE)}
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
        {tPolicyDetail("selectMemberPrompt", LANGUAGE)}
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("viewPolicyList", LANGUAGE)}
      >
        {tPolicyDetail("viewPolicyList", LANGUAGE)}
      </button>
    </div>
  </main>
{:else if loading}
  <!-- Loading State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("backToList", LANGUAGE)}
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {tPolicyDetail("backToList", LANGUAGE)}
      </button>
    </div>
    <LoadingStates
      variant="detail"
      message={tPolicyDetail("loadingDetails", LANGUAGE)}
    />
  </main>
{:else if error}
  <!-- Error State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("backToList", LANGUAGE)}
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {tPolicyDetail("backToList", LANGUAGE)}
      </button>
    </div>
    <ErrorStates
      variant="api"
      {error}
      on:retry={handleRetry}
      on:goBack={handleBackNavigation}
      message={tPolicyDetail("errorLoadingDetails", LANGUAGE)}
    />
  </main>
{:else if !selectedPolicy}
  <!-- Policy Not Found State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("backToList", LANGUAGE)}
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {tPolicyDetail("backToList", LANGUAGE)}
      </button>
    </div>
    <ErrorStates
      variant="not-found"
      on:goBack={handleBackNavigation}
      message={tPolicyDetail("policyNotFound", LANGUAGE)}
    />
  </main>
{:else}
  <!-- Policy Detail Content -->
  <main class="min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8">
    <!-- Back Navigation -->
    <div class="max-w-7xl mx-auto mb-6 sm:mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label={tPolicyDetail("backToList", LANGUAGE)}
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {tPolicyDetail("backToList", LANGUAGE)}
      </button>
    </div>

    <!-- Main Content Container -->
    <div class="max-w-7xl mx-auto">
      <!-- Policy Header -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <div
          class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
        >
          <div class="flex items-center mb-4 lg:mb-0">
            <span class="text-4xl mr-4" aria-hidden="true">📄</span>
            <div>
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                {tPolicyDetail("pageTitle", LANGUAGE)}
              </h1>
              <div class="flex items-start">
                <svg
                  class="w-4 h-4 text-gray-400 mt-1 mr-2 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <div class="text-gray-600">
                  {tPolicyDetail("member", LANGUAGE)}:
                  <!-- <span class="font-medium">{memberDisplayName}</span> -->
                  <span class="text-gray-900">
                    {selectedMember.titleEN || ""}
                    {selectedMember.nameEN || ""}
                    {selectedMember.surnameEN || ""}
                  </span>
                  {#if selectedMember?.memberCode}
                    <span class="text-sm text-gray-500 ml-2"
                      >({tPolicyDetail("memberCode", LANGUAGE)}: {selectedMember.memberCode})</span
                    >
                  {/if}
                </div>
              </div>
            </div>
          </div>
          {#if selectedMember}
            <div class="flex flex-col sm:flex-row sm:items-center gap-3">
              <span
                class="px-4 py-2 rounded-full text-sm font-medium border text-center
                       {statusColors[selectedMember.memberStatus] ||
                  statusColors.Active}"
                aria-label="{tPolicyDetail(
                  'memberStatus',
                  LANGUAGE,
                )}: {selectedMember.memberStatus}"
              >
                {selectedMember.memberStatus}
              </span>
              {#if selectedMember.vip === "Y"}
                <span
                  class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200"
                >
                  VIP
                </span>
              {/if}
              {#if selectedMember.cardType && selectedMember.cardType !== "Standard"}
                <span
                  class="px-3 py-1 rounded-full text-xs font-medium
                         {selectedMember.cardType === 'Gold'
                    ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                    : selectedMember.cardType === 'Platinum'
                      ? 'bg-gray-100 text-gray-800 border border-gray-200'
                      : selectedMember.cardType === 'Diamond'
                        ? 'bg-blue-100 text-blue-800 border border-blue-200'
                        : 'bg-gray-100 text-gray-800 border border-gray-200'}"
                >
                  {selectedMember.cardType}
                </span>
              {/if}
            </div>
          {/if}
        </div>
      </div>

      <!-- Responsive Grid Layout -->
      <div
        class="grid gap-6
                  grid-cols-1
                  lg:grid-cols-3
                  xl:grid-cols-4"
      >
        <!-- Main Content Area -->
        <div class="lg:col-span-3 xl:col-span-4 space-y-6">
          <!-- Policy Overview -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              {tPolicyDetail("policyInfo", LANGUAGE)}
            </h2>
            <div class="grid gap-4 sm:grid-cols-2">
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("policyNo", LANGUAGE)}
                </div>
                <p class="text-lg font-semibold text-gray-900">
                  {policyListData?.PolicyNo ||
                    tPolicyDetail("notSpecified", LANGUAGE)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("certificateNo", LANGUAGE)}
                </div>
                <p class="text-lg font-semibold text-gray-900">
                  {policyListData?.CertificateNo ||
                    tPolicyDetail("notSpecified", LANGUAGE)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("planName", LANGUAGE)}
                </div>
                <p class="text-gray-900">
                  {policyListData?.PlanName ||
                    tPolicyDetail("notSpecified", LANGUAGE)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("effectiveFrom", LANGUAGE)}
                </div>
                <p class="text-gray-900">
                  {formatDate(policyListData?.PlanEffFrom)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("insurerName", LANGUAGE)}
                </div>
                <p class="text-gray-900">
                  {policyListData?.InsurerNameEN ||
                    policyListData?.InsurerName ||
                    tPolicyDetail("notSpecified", LANGUAGE)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  {tPolicyDetail("effectiveTo", LANGUAGE)}
                </div>
                <p class="text-gray-900">
                  {formatDate(policyListData?.PlanEffTo)}
                </p>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
              <div class="text-sm font-medium text-gray-500 mb-2">
                {tPolicyDetail("contractCompany", LANGUAGE)}
              </div>
              <p class="text-gray-700 leading-relaxed">
                {policyListData?.CompanyNameEN ||
                  policyListData?.CompanyName ||
                  tPolicyDetail("notSpecified", LANGUAGE)}
              </p>
            </div>
          </section>

          <!-- Main Benefits -->
          {#if policyDetailList.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                {tPolicyDetail("mainBenefits", LANGUAGE)}
              </h2>
              <div class="space-y-4">
                {#each policyDetailList as benefit}
                  <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex justify-between items-start">
                      <div>
                        <h3 class="font-medium text-gray-900">
                          {benefit.MainBenefitEN}
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">
                          {benefit.MainBenefit}
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                          {benefit.MainPlanLimitDesc}
                        </p>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-semibold text-blue-600">
                          {formatCurrency(
                            parseInt(benefit.MainPlanAmount) || 0,
                          )}
                        </div>
                        <div class="text-sm text-gray-500">
                          {benefit.MainPlanUnit1}/{benefit.MainPlanUnit2}
                        </div>
                        <div class="text-sm text-green-600 mt-1">
                          {tPolicyDetail("remaining", LANGUAGE)}: {formatCurrency(
                            parseInt(benefit.MainPlanBalance) || 0,
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Detailed Benefits -->
          {#if benefitList.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                {tPolicyDetail("detailedBenefits", LANGUAGE)}
              </h2>
              <div class="space-y-4">
                {#each benefitList as benefit}
                  <div
                    class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div class="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 class="font-medium text-gray-900">
                          {benefit.BenefitEN}
                        </h3>
                        <p class="text-sm text-gray-500 mt-1">
                          {benefit.SubBenefitEN}
                        </p>
                      </div>
                      <div class="space-y-2">
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500"
                            >{tPolicyDetail("limitPerVisit", LANGUAGE)}:</span
                          >
                          <span class="text-sm font-medium">
                            {formatCurrency(parseInt(benefit.LimitAmt) || 0)}
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500"
                            >{tPolicyDetail("limitPerYear", LANGUAGE)}:</span
                          >
                          <span class="text-sm font-medium">
                            {formatCurrency(parseInt(benefit.ComLimitAmt) || 0)}
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500"
                            >{tPolicyDetail(
                              "remainingPerYear",
                              LANGUAGE,
                            )}:</span
                          >
                          <span class="text-sm font-medium text-green-600">
                            {formatCurrency(
                              parseInt(benefit.BalComLimitAmt) || 0,
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Contract Conditions -->
          {#if contractConditions.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                {tPolicyDetail("contractConditions", LANGUAGE)}
              </h2>
              <div class="space-y-4">
                {#each contractConditions as condition}
                  <div
                    class="p-4 border border-gray-200 rounded-lg
                           {condition.ConditionType === 'Exclusion'
                      ? 'bg-red-50 border-red-200'
                      : condition.ConditionType === 'Waiting Period'
                        ? 'bg-yellow-50 border-yellow-200'
                        : 'bg-gray-50'}"
                  >
                    <div class="flex justify-between items-start mb-2">
                      <h3 class="font-medium text-gray-900">
                        {condition.ConditionType === "Exclusion"
                          ? tPolicyDetail("exclusion", LANGUAGE)
                          : condition.ConditionType === "Waiting Period"
                            ? tPolicyDetail("waitingPeriod", LANGUAGE)
                            : condition.ConditionType}
                      </h3>
                      <span
                        class="px-2 py-1 text-xs rounded-full
                               {condition.ConditionApply === 'Y'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'}"
                      >
                        {condition.ConditionApply === "Y"
                          ? tPolicyDetail("effective", LANGUAGE)
                          : tPolicyDetail("notEffective", LANGUAGE)}
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-2">
                      <!-- {condition.ConditionDetail} -->
                    </p>
                    <div
                      class="grid gap-2 sm:grid-cols-2 text-xs text-gray-500"
                    >
                      <div>
                        {tPolicyDetail("startDate", LANGUAGE)}: {formatDate(
                          condition.EffFromDate,
                        )}
                      </div>
                      <div>
                        {tPolicyDetail("endDate", LANGUAGE)}: {formatDate(
                          condition.EffToDate,
                        )}
                      </div>
                    </div>
                    {#if condition.Remarks}
                      <p class="text-xs text-gray-500 mt-2 italic">
                        {tPolicyDetail("remarks", LANGUAGE)}: {condition.Remarks}
                      </p>
                    {/if}
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Member Conditions -->
          {#if memberConditions.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                {tPolicyDetail("memberConditions", LANGUAGE)}
              </h2>
              <div class="space-y-4">
                {#each memberConditions as condition}
                  <div
                    class="p-4 border border-gray-200 rounded-lg
                           {condition.ConditionType === 'Medical History'
                      ? 'bg-blue-50 border-blue-200'
                      : condition.ConditionType === 'Medication'
                        ? 'bg-purple-50 border-purple-200'
                        : 'bg-gray-50'}"
                  >
                    <div class="flex justify-between items-start mb-2">
                      <h3 class="font-medium text-gray-900">
                        {condition.ConditionType === "Medical History"
                          ? tPolicyDetail("medicalHistory", LANGUAGE)
                          : condition.ConditionType === "Medication"
                            ? tPolicyDetail("medication", LANGUAGE)
                            : condition.ConditionType}
                      </h3>
                      <span
                        class="px-2 py-1 text-xs rounded-full
                               {condition.Action === 'Monitor'
                          ? 'bg-yellow-100 text-yellow-800'
                          : condition.Action === 'Alert'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'}"
                      >
                        {condition.Action === "Monitor"
                          ? tPolicyDetail("monitor", LANGUAGE)
                          : condition.Action === "Alert"
                            ? tPolicyDetail("alert", LANGUAGE)
                            : condition.Action}
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-2">
                      <!-- {condition.ConditionDetail} -->
                    </p>
                    <div
                      class="grid gap-2 sm:grid-cols-2 text-xs text-gray-500"
                    >
                      <div>
                        {tPolicyDetail("startDate", LANGUAGE)}: {formatDate(
                          condition.EffFromDate,
                        )}
                      </div>
                      <div>
                        {tPolicyDetail("endDate", LANGUAGE)}: {formatDate(
                          condition.EffToDate,
                        )}
                      </div>
                    </div>
                    {#if condition.Remarks}
                      <p class="text-xs text-gray-500 mt-2 italic">
                        {tPolicyDetail("remarks", LANGUAGE)}: {condition.Remarks}
                      </p>
                    {/if}
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Enhanced Claims History -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            aria-labelledby="claims-history-heading"
          >
            <div
              class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6"
            >
              <h2
                id="claims-history-heading"
                class="text-xl font-semibold text-gray-900 mb-2 sm:mb-0"
              >
                {tPolicyDetail("claimsHistory", LANGUAGE)}
              </h2>
              {#if claimHistory.length > 0}
                <div class="text-sm text-gray-500">
                  {tPolicyDetail("totalClaims", LANGUAGE)}
                  {claimHistory.length}
                  {tPolicyDetail("claimsCount", LANGUAGE)}
                </div>
              {/if}
            </div>

            {#if claimHistory.length > 0}
              <div class="space-y-4">
                {#each claimHistory as claim, index}
                  <!-- Add "hover:bg-gray-50 transition-colors duration-200" for hover effect -->
                  <article
                    class="border border-gray-200 rounded-lg p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200"
                    aria-labelledby="claim-{index}-title"
                  >
                    <!-- Claim Header -->
                    <div
                      class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4"
                    >
                      <div class="flex-1 mb-4 lg:mb-0">
                        <div
                          class="flex flex-col sm:flex-row sm:items-center sm:gap-3 mb-2"
                        >
                          <h3
                            id="claim-{index}-title"
                            class="text-lg font-semibold text-gray-900"
                          >
                            {tPolicyDetail("claimNumber", LANGUAGE)}
                            {claim.ClaimNo ||
                              claim.ClaimID ||
                              tPolicyDetail("notSpecified", LANGUAGE)}
                          </h3>
                          <div class="flex flex-wrap gap-2 mt-2 sm:mt-0">
                            <!-- Claim Status Badge -->
                            <span
                              class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border
                                     {getClaimStatusColor(claim.ClaimStatus)}"
                              aria-label="{tPolicyDetail(
                                'claimStatus',
                                LANGUAGE,
                              )}: {formatClaimStatus(claim.ClaimStatus)}"
                            >
                              {formatClaimStatus(claim.ClaimStatus)}
                            </span>
                            <!-- Claim Type Badge -->
                            {#if claim.ClaimType || claim.ServiceType}
                              <span
                                class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border
                                       {getClaimTypeColor(
                                  claim.ClaimType || claim.ServiceType,
                                )}"
                                aria-label="{tPolicyDetail(
                                  'claimType',
                                  LANGUAGE,
                                )}: {formatClaimType(
                                  claim.ClaimType || claim.ServiceType,
                                )}"
                              >
                                {formatClaimType(
                                  claim.ClaimType || claim.ServiceType,
                                )}
                              </span>
                            {/if}
                          </div>
                        </div>

                        <!-- Diagnosis/Description -->
                        <div class="mb-3">
                          <p class="text-gray-700 leading-relaxed">
                            {claim.DiagEN ||
                              claim.DiagTH ||
                              claim.Description ||
                              tPolicyDetail("noDetails", LANGUAGE)}
                          </p>
                        </div>
                      </div>

                      <!-- Amount Information -->
                      <div class="lg:text-right lg:ml-6">
                        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                          <div>
                            <div class="text-sm text-gray-500 mb-1">
                              {tPolicyDetail("paidAmount", LANGUAGE)}
                            </div>
                            <div class="text-xl font-bold text-green-600">
                              {formatCurrency(parseInt(claim.PayableAmt) || 0)}
                            </div>
                          </div>
                          <div>
                            <div class="text-sm text-gray-500 mb-1">
                              {tPolicyDetail("claimedAmount", LANGUAGE)}
                            </div>
                            <div class="text-lg font-semibold text-gray-900">
                              {formatCurrency(parseInt(claim.IncurredAmt) || 0)}
                            </div>
                          </div>
                          {#if claim.PayableAmt && claim.IncurredAmt && parseInt(claim.PayableAmt) !== parseInt(claim.IncurredAmt)}
                            <div>
                              <div class="text-sm text-gray-500 mb-1">
                                {tPolicyDetail("difference", LANGUAGE)}
                              </div>
                              <div class="text-sm font-medium text-red-600">
                                -{formatCurrency(
                                  parseInt(claim.IncurredAmt) -
                                    parseInt(claim.PayableAmt),
                                )}
                              </div>
                            </div>
                          {/if}
                        </div>
                      </div>
                    </div>

                    <!-- Claim Details Grid -->
                    <div
                      class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 text-sm"
                    >
                      <!-- Visit/Service Date -->
                      {#if claim.VisitDate || claim.ServiceDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              {tPolicyDetail("serviceDate", LANGUAGE)}
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(
                                claim.VisitDate || claim.ServiceDate,
                              )}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Claim Date -->
                      {#if claim.ClaimDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              {tPolicyDetail("claimDate", LANGUAGE)}
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(claim.ClaimDate)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Settlement Date -->
                      {#if claim.SettlementDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              {tPolicyDetail("settlementDate", LANGUAGE)}
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(claim.SettlementDate)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Provider Information -->
                      {#if claim.ProviderEN || claim.ProviderTH || claim.ProviderName}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              {tPolicyDetail("provider", LANGUAGE)}
                            </div>
                            <div class="text-gray-600">
                              {claim.ProviderEN ||
                                claim.ProviderTH ||
                                claim.ProviderName ||
                                tPolicyDetail("notSpecified", LANGUAGE)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Policy Number -->
                      {#if claim.PolicyNo}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              {tPolicyDetail("policyNo", LANGUAGE)}
                            </div>
                            <div class="text-gray-600 font-mono">
                              {claim.PolicyNo}
                            </div>
                          </div>
                        </div>
                      {/if}
                    </div>
                  </article>
                {/each}
              </div>
            {:else}
              <div class="text-center py-12">
                <div class="text-6xl mb-4" aria-hidden="true">📋</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  {tPolicyDetail("noClaimsFound", LANGUAGE)}
                </h3>
                <p class="text-gray-500 max-w-md mx-auto">
                  {tPolicyDetail("noClaimsDescription", LANGUAGE)}
                </p>
              </div>
            {/if}
          </section>
        </div>
      </div>
    </div>
  </main>
{/if}
