<!--
  LanguageToggle Component

  A language toggle button component for switching between Thai and English.

  Features:
  - Toggle between Thai (TH) and English (EN) languages
  - Responsive design with Tailwind CSS
  - Accessible design with ARIA labels and keyboard navigation
  - Visual indicators for current language
  - Smooth transitions and hover effects
  - Proper focus management

  Usage:
  <LanguageToggle />
  <LanguageToggle compact={true} />
-->

<script>
  import { 
    currentLanguageStore, 
    languageDisplayName, 
    toggleLanguage,
    SUPPORTED_LANGUAGES 
  } from "../stores/languageStore.js";

  // Props
  export let compact = false; // Compact mode for smaller spaces
  export let showLabel = true; // Whether to show language label

  // Reactive store subscriptions
  $: currentLanguage = $currentLanguageStore;
  $: displayName = $languageDisplayName;
  $: isThaiActive = currentLanguage === SUPPORTED_LANGUAGES.TH;
  $: isEnglishActive = currentLanguage === SUPPORTED_LANGUAGES.EN;

  // Handle language toggle
  function handleToggle() {
    toggleLanguage();
  }

  // Get next language for accessibility
  function getNextLanguage() {
    return currentLanguage === SUPPORTED_LANGUAGES.TH 
      ? 'English' 
      : 'ไทย';
  }
</script>

<!-- Language Toggle Button -->
<div class="flex items-center space-x-2">
  {#if showLabel && !compact}
    <span class="text-sm text-gray-600 hidden sm:inline">
      Language:
    </span>
  {/if}
  
  <button
    type="button"
    class="inline-flex items-center justify-center
           {compact ? 'px-2 py-1' : 'px-3 py-2'}
           bg-white border border-gray-300 rounded-md shadow-sm
           text-sm font-medium text-gray-700
           hover:bg-gray-50 hover:border-gray-400
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
           transition-all duration-200 ease-in-out
           min-w-[80px]"
    on:click={handleToggle}
    aria-label="Switch to {getNextLanguage()}"
    title="Switch to {getNextLanguage()}"
  >
    <!-- Language Icons and Text -->
    <div class="flex items-center space-x-1">
      <!-- Thai Flag/Icon -->
      <span 
        class="text-base {isThaiActive ? 'opacity-100' : 'opacity-50'} transition-opacity duration-200"
        aria-hidden="true"
      >
        🇹🇭
      </span>
      
      <!-- Separator -->
      <span class="text-gray-400 text-xs" aria-hidden="true">|</span>
      
      <!-- English Flag/Icon -->
      <span 
        class="text-base {isEnglishActive ? 'opacity-100' : 'opacity-50'} transition-opacity duration-200"
        aria-hidden="true"
      >
        🇺🇸
      </span>
    </div>
    
    {#if !compact}
      <!-- Current Language Display -->
      <span class="ml-2 text-xs font-medium">
        {displayName}
      </span>
    {/if}
  </button>
</div>

<!-- Alternative compact toggle design -->
{#if compact}
  <!-- Screen reader only text for compact mode -->
  <span class="sr-only">
    Current language: {displayName}. Click to switch to {getNextLanguage()}.
  </span>
{/if}

<style>
  /* Custom styles for smooth transitions */
  button:hover span {
    transform: scale(1.05);
  }
  
  button:active {
    transform: scale(0.98);
  }
</style>
