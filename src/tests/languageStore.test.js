/**
 * Language Store Tests
 * 
 * Tests for the language store functionality including:
 * - Language switching
 * - Session persistence
 * - Validation
 * - Derived stores
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
  currentLanguageStore,
  isThaiLanguage,
  isEnglishLanguage,
  languageDisplayName,
  setLanguage,
  toggleLanguage,
  isValidLanguage,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
  initializeLanguageStore
} from '../stores/languageStore.js';

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('Language Store', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Reset store to default
    currentLanguageStore.set(DEFAULT_LANGUAGE);
  });

  describe('Language Validation', () => {
    it('should validate supported languages', () => {
      expect(isValidLanguage(SUPPORTED_LANGUAGES.TH)).toBe(true);
      expect(isValidLanguage(SUPPORTED_LANGUAGES.EN)).toBe(true);
      expect(isValidLanguage('FR')).toBe(false);
      expect(isValidLanguage('')).toBe(false);
      expect(isValidLanguage(null)).toBe(false);
      expect(isValidLanguage(undefined)).toBe(false);
    });
  });

  describe('Language Setting', () => {
    it('should set valid language', () => {
      setLanguage(SUPPORTED_LANGUAGES.EN);
      expect(get(currentLanguageStore)).toBe(SUPPORTED_LANGUAGES.EN);
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'insurance_portal_language',
        SUPPORTED_LANGUAGES.EN
      );
    });

    it('should fallback to default for invalid language', () => {
      setLanguage('INVALID');
      expect(get(currentLanguageStore)).toBe(DEFAULT_LANGUAGE);
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'insurance_portal_language',
        DEFAULT_LANGUAGE
      );
    });
  });

  describe('Language Toggle', () => {
    it('should toggle from Thai to English', () => {
      currentLanguageStore.set(SUPPORTED_LANGUAGES.TH);
      toggleLanguage();
      expect(get(currentLanguageStore)).toBe(SUPPORTED_LANGUAGES.EN);
    });

    it('should toggle from English to Thai', () => {
      currentLanguageStore.set(SUPPORTED_LANGUAGES.EN);
      toggleLanguage();
      expect(get(currentLanguageStore)).toBe(SUPPORTED_LANGUAGES.TH);
    });
  });

  describe('Derived Stores', () => {
    it('should correctly identify Thai language', () => {
      currentLanguageStore.set(SUPPORTED_LANGUAGES.TH);
      expect(get(isThaiLanguage)).toBe(true);
      expect(get(isEnglishLanguage)).toBe(false);
    });

    it('should correctly identify English language', () => {
      currentLanguageStore.set(SUPPORTED_LANGUAGES.EN);
      expect(get(isThaiLanguage)).toBe(false);
      expect(get(isEnglishLanguage)).toBe(true);
    });

    it('should provide correct display names', () => {
      currentLanguageStore.set(SUPPORTED_LANGUAGES.TH);
      expect(get(languageDisplayName)).toBe('ไทย');

      currentLanguageStore.set(SUPPORTED_LANGUAGES.EN);
      expect(get(languageDisplayName)).toBe('English');
    });
  });

  describe('Session Persistence', () => {
    it('should initialize from session storage', () => {
      mockSessionStorage.getItem.mockReturnValue(SUPPORTED_LANGUAGES.EN);
      initializeLanguageStore();
      expect(get(currentLanguageStore)).toBe(SUPPORTED_LANGUAGES.EN);
    });

    it('should fallback to default when session storage is empty', () => {
      mockSessionStorage.getItem.mockReturnValue(null);
      initializeLanguageStore();
      expect(get(currentLanguageStore)).toBe(DEFAULT_LANGUAGE);
    });

    it('should fallback to default when session storage has invalid value', () => {
      mockSessionStorage.getItem.mockReturnValue('INVALID');
      initializeLanguageStore();
      expect(get(currentLanguageStore)).toBe(DEFAULT_LANGUAGE);
    });
  });

  describe('Error Handling', () => {
    it('should handle sessionStorage errors gracefully', () => {
      mockSessionStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      
      expect(() => initializeLanguageStore()).not.toThrow();
      expect(get(currentLanguageStore)).toBe(DEFAULT_LANGUAGE);
    });

    it('should handle setItem errors gracefully', () => {
      mockSessionStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      
      expect(() => setLanguage(SUPPORTED_LANGUAGES.EN)).not.toThrow();
      expect(get(currentLanguageStore)).toBe(SUPPORTED_LANGUAGES.EN);
    });
  });
});
