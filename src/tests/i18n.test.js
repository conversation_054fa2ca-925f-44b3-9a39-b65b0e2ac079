/**
 * Internationalization Utilities Tests
 * 
 * Tests for the i18n utilities including:
 * - Text translations
 * - Policy type translations
 * - Currency formatting
 * - Date formatting
 * - Error handling
 */

import { describe, it, expect } from 'vitest';
import {
  t,
  getPolicyTypeTranslation,
  formatCurrencyByLanguage,
  formatDateByLanguage,
  translations
} from '../utils/i18n.js';
import { SUPPORTED_LANGUAGES } from '../stores/languageStore.js';

describe('Internationalization Utilities', () => {
  describe('Text Translation (t function)', () => {
    it('should return Thai translation by default', () => {
      expect(t('pageTitle')).toBe('กรมธรรม์ประกันภัย');
      expect(t('selectMember')).toBe('เลือกสมาชิก');
    });

    it('should return English translation when specified', () => {
      expect(t('pageTitle', SUPPORTED_LANGUAGES.EN)).toBe('Insurance Policies');
      expect(t('selectMember', SUPPORTED_LANGUAGES.EN)).toBe('Select Member');
    });

    it('should fallback to Thai when English translation missing', () => {
      // Test with a key that might not have English translation
      const result = t('pageTitle', SUPPORTED_LANGUAGES.EN);
      expect(result).toBeTruthy();
    });

    it('should return fallback text for missing keys', () => {
      expect(t('nonExistentKey', SUPPORTED_LANGUAGES.TH, 'Fallback')).toBe('Fallback');
      expect(t('nonExistentKey', SUPPORTED_LANGUAGES.EN, 'Fallback')).toBe('Fallback');
    });

    it('should return key when no translation or fallback provided', () => {
      expect(t('nonExistentKey')).toBe('nonExistentKey');
    });
  });

  describe('Policy Type Translation', () => {
    it('should translate policy types to Thai', () => {
      expect(getPolicyTypeTranslation('Auto', SUPPORTED_LANGUAGES.TH)).toBe('ประกันรถยนต์');
      expect(getPolicyTypeTranslation('Health', SUPPORTED_LANGUAGES.TH)).toBe('ประกันสุขภาพ');
      expect(getPolicyTypeTranslation('Life', SUPPORTED_LANGUAGES.TH)).toBe('ประกันชีวิต');
    });

    it('should translate policy types to English', () => {
      expect(getPolicyTypeTranslation('Auto', SUPPORTED_LANGUAGES.EN)).toBe('Auto Insurance');
      expect(getPolicyTypeTranslation('Health', SUPPORTED_LANGUAGES.EN)).toBe('Health Insurance');
      expect(getPolicyTypeTranslation('Life', SUPPORTED_LANGUAGES.EN)).toBe('Life Insurance');
    });

    it('should return original type for unknown types', () => {
      expect(getPolicyTypeTranslation('UnknownType', SUPPORTED_LANGUAGES.TH)).toBe('UnknownType');
      expect(getPolicyTypeTranslation('UnknownType', SUPPORTED_LANGUAGES.EN)).toBe('UnknownType');
    });

    it('should handle empty or null policy types', () => {
      expect(getPolicyTypeTranslation('', SUPPORTED_LANGUAGES.TH)).toBe('');
      expect(getPolicyTypeTranslation(null, SUPPORTED_LANGUAGES.TH)).toBe('');
      expect(getPolicyTypeTranslation(undefined, SUPPORTED_LANGUAGES.TH)).toBe('');
    });
  });

  describe('Currency Formatting', () => {
    it('should format currency in Thai locale', () => {
      const result = formatCurrencyByLanguage(1000, SUPPORTED_LANGUAGES.TH);
      expect(result).toContain('1,000');
      expect(result).toContain('฿'); // Thai Baht symbol
    });

    it('should format currency in English locale', () => {
      const result = formatCurrencyByLanguage(1000, SUPPORTED_LANGUAGES.EN);
      expect(result).toContain('1,000');
      expect(result).toContain('THB'); // Currency code
    });

    it('should handle decimal amounts', () => {
      const resultTH = formatCurrencyByLanguage(1000.50, SUPPORTED_LANGUAGES.TH);
      const resultEN = formatCurrencyByLanguage(1000.50, SUPPORTED_LANGUAGES.EN);

      expect(resultTH).toContain('1,000.5');
      expect(resultEN).toContain('1,000.5');
    });

    it('should handle zero amounts', () => {
      const resultTH = formatCurrencyByLanguage(0, SUPPORTED_LANGUAGES.TH);
      const resultEN = formatCurrencyByLanguage(0, SUPPORTED_LANGUAGES.EN);

      expect(resultTH).toContain('0');
      expect(resultEN).toContain('0');
    });

    it('should handle invalid amounts', () => {
      expect(formatCurrencyByLanguage(NaN, SUPPORTED_LANGUAGES.TH)).toBe('ไม่มีข้อมูล');
      expect(formatCurrencyByLanguage(NaN, SUPPORTED_LANGUAGES.EN)).toBe('N/A');
      expect(formatCurrencyByLanguage('invalid', SUPPORTED_LANGUAGES.TH)).toBe('ไม่มีข้อมูล');
      expect(formatCurrencyByLanguage('invalid', SUPPORTED_LANGUAGES.EN)).toBe('N/A');
    });
  });

  describe('Date Formatting', () => {
    const testDate = new Date('2024-01-15');

    it('should format date in Thai locale', () => {
      const result = formatDateByLanguage(testDate, SUPPORTED_LANGUAGES.TH);
      expect(result).toContain('2567'); // Buddhist calendar year
      expect(result).toContain('15');
    });

    it('should format date in English locale', () => {
      const result = formatDateByLanguage(testDate, SUPPORTED_LANGUAGES.EN);
      expect(result).toContain('2024');
      expect(result).toContain('15');
      expect(result).toContain('January');
    });

    it('should handle string dates', () => {
      const result = formatDateByLanguage('2024-01-15', SUPPORTED_LANGUAGES.EN);
      expect(result).toContain('2024');
      expect(result).toContain('January');
    });

    it('should handle null/undefined dates', () => {
      expect(formatDateByLanguage(null, SUPPORTED_LANGUAGES.TH)).toBe('ไม่มีข้อมูล');
      expect(formatDateByLanguage(null, SUPPORTED_LANGUAGES.EN)).toBe('N/A');
      expect(formatDateByLanguage(undefined, SUPPORTED_LANGUAGES.TH)).toBe('ไม่มีข้อมูล');
      expect(formatDateByLanguage(undefined, SUPPORTED_LANGUAGES.EN)).toBe('N/A');
    });

    it('should handle invalid dates', () => {
      expect(formatDateByLanguage('invalid-date', SUPPORTED_LANGUAGES.TH)).toBe('วันที่ไม่ถูกต้อง');
      expect(formatDateByLanguage('invalid-date', SUPPORTED_LANGUAGES.EN)).toBe('Invalid date');
      expect(formatDateByLanguage(new Date('invalid'), SUPPORTED_LANGUAGES.TH)).toBe('วันที่ไม่ถูกต้อง');
      expect(formatDateByLanguage(new Date('invalid'), SUPPORTED_LANGUAGES.EN)).toBe('Invalid date');
    });
  });

  describe('Translation Coverage', () => {
    it('should have all required translation keys', () => {
      const requiredKeys = [
        'pageTitle',
        'selectMember',
        'selectInsurer',
        'loading',
        'retry',
        'memberInfo',
        'citizenId',
        'policyNumber',
        'coverageAmount',
        'premium'
      ];

      requiredKeys.forEach(key => {
        expect(translations[key]).toBeDefined();
        expect(translations[key][SUPPORTED_LANGUAGES.TH]).toBeDefined();
        expect(translations[key][SUPPORTED_LANGUAGES.EN]).toBeDefined();
      });
    });

    it('should have policy type translations', () => {
      const policyTypes = ['Auto', 'Home', 'Life', 'Health', 'Business'];

      policyTypes.forEach(type => {
        expect(translations.policyTypes[type]).toBeDefined();
        expect(translations.policyTypes[type][SUPPORTED_LANGUAGES.TH]).toBeDefined();
        expect(translations.policyTypes[type][SUPPORTED_LANGUAGES.EN]).toBeDefined();
      });
    });
  });
});
