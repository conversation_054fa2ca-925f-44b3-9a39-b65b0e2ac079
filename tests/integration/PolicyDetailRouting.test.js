/**
 * PolicyDetail Routing Integration Tests
 * 
 * Tests to verify that the routing system correctly switches between
 * Thai and English versions of PolicyDetail based on language settings.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import App from '../../src/App.svelte';
import { SUPPORTED_LANGUAGES } from '../../src/stores/languageStore.js';

// Mock all the stores and API
vi.mock('../../src/stores/dataStore.js', () => ({
  policyDetailStore: {
    subscribe: vi.fn((callback) => {
      callback({ data: null, loading: false, error: null });
      return () => { };
    })
  },
  loadPolicyDetail: vi.fn()
}));

vi.mock('../../src/stores/memberStore.js', () => ({
  selectedMemberStore: {
    subscribe: vi.fn((callback) => {
      callback({
        memberCode: 'TEST001',
        citizenID: '*************',
        insurerCode: 'INS001',
        memberStatus: 'Active'
      });
      return () => { };
    })
  },
  selectedMemberDisplayName: {
    subscribe: vi.fn((callback) => {
      callback('Test Member');
      return () => { };
    })
  },
  selectedMemberShortName: {
    subscribe: vi.fn((callback) => {
      callback('Test');
      return () => { };
    })
  },
  initializeMemberStore: vi.fn()
}));

vi.mock('../../src/stores/languageStore.js', async () => {
  const actual = await vi.importActual('../../src/stores/languageStore.js');
  return {
    ...actual,
    currentLanguageStore: {
      subscribe: vi.fn((callback) => {
        callback(SUPPORTED_LANGUAGES.TH); // Default to Thai
        return () => { };
      })
    },
    initializeLanguageStore: vi.fn()
  };
});

vi.mock('../../src/api/index.js', () => ({
  default: {
    policies: {
      searchByCitizenId: vi.fn().mockResolvedValue({
        success: true,
        data: [{
          PolicyNo: 'POL001',
          CertificateNo: 'CERT001',
          PlanName: 'Test Plan',
          InsurerName: 'Test Insurer',
          CompanyName: 'Test Company'
        }]
      })
    }
  }
}));

describe('PolicyDetail Routing Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should have App component available for routing', async () => {
    // Test that App component can be imported (basic integration test)
    const AppModule = await import('../../src/App.svelte');
    expect(AppModule.default).toBeTruthy();
  });
});

describe('Language Store Integration', () => {
  it('should support both Thai and English languages', () => {
    expect(SUPPORTED_LANGUAGES.TH).toBe('TH');
    expect(SUPPORTED_LANGUAGES.EN).toBe('EN');
  });

  it('should have proper language constants', () => {
    expect(Object.keys(SUPPORTED_LANGUAGES)).toContain('TH');
    expect(Object.keys(SUPPORTED_LANGUAGES)).toContain('EN');
  });
});

describe('Component File Structure', () => {
  it('should be able to import PolicyDetailEN component', async () => {
    // Test that PolicyDetailEN can be imported without errors
    const PolicyDetailEN = await import('../../src/PolicyDetailEN.svelte');
    expect(PolicyDetailEN.default).toBeTruthy();
  });

  it('should be able to import original PolicyDetail component', async () => {
    // Test that original PolicyDetail still works
    const PolicyDetail = await import('../../src/PolicyDetail.svelte');
    expect(PolicyDetail.default).toBeTruthy();
  });

  it('should be able to import i18n utilities', async () => {
    // Test that i18n utilities can be imported
    const i18n = await import('../../src/utils/i18n.js');
    expect(i18n.tPolicyDetail).toBeTruthy();
    expect(i18n.formatCurrencyByLanguage).toBeTruthy();
    expect(i18n.formatDateByLanguage).toBeTruthy();
    expect(i18n.getClaimStatusTranslation).toBeTruthy();
    expect(i18n.getClaimTypeTranslation).toBeTruthy();
  });
});

describe('Build Integration', () => {
  it('should have all required dependencies for PolicyDetailEN', async () => {
    // Test that all imports used by PolicyDetailEN are available
    const stores = await import('../../src/stores/dataStore.js');
    const memberStore = await import('../../src/stores/memberStore.js');
    const languageStore = await import('../../src/stores/languageStore.js');
    const api = await import('../../src/api/index.js');
    const i18n = await import('../../src/utils/i18n.js');

    expect(stores).toBeTruthy();
    expect(memberStore).toBeTruthy();
    expect(languageStore).toBeTruthy();
    expect(api).toBeTruthy();
    expect(i18n).toBeTruthy();
  });

  it('should have LoadingStates and ErrorStates components available', async () => {
    // Test that shared components are available
    const LoadingStates = await import('../../src/components/LoadingStates.svelte');
    const ErrorStates = await import('../../src/components/ErrorStates.svelte');

    expect(LoadingStates.default).toBeTruthy();
    expect(ErrorStates.default).toBeTruthy();
  });
});

describe('Translation Completeness', () => {
  it('should have comprehensive PolicyDetail translations', async () => {
    const i18nModule = await import('../../src/utils/i18n.js');
    const languageModule = await import('../../src/stores/languageStore.js');

    const { tPolicyDetail } = i18nModule;
    const { SUPPORTED_LANGUAGES } = languageModule;

    const testKeys = [
      'pageTitle',
      'backToList',
      'noMemberSelected',
      'policyInfo',
      'mainBenefits',
      'detailedBenefits',
      'contractConditions',
      'memberConditions',
      'claimsHistory',
      'loadingDetails',
      'errorLoadingDetails'
    ];

    testKeys.forEach(key => {
      const thaiTranslation = tPolicyDetail(key, SUPPORTED_LANGUAGES.TH);
      const englishTranslation = tPolicyDetail(key, SUPPORTED_LANGUAGES.EN);

      expect(thaiTranslation).toBeTruthy();
      expect(englishTranslation).toBeTruthy();
      expect(thaiTranslation).not.toBe(englishTranslation); // Should be different
    });
  });
});
