/**
 * PolicyDetailEN Component Tests
 * 
 * Tests for the English version of the PolicyDetail component
 * to ensure proper internationalization and functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import PolicyDetailEN from '../src/PolicyDetailEN.svelte';
import { SUPPORTED_LANGUAGES } from '../src/stores/languageStore.js';
import {
  tPolicyDetail,
  formatCurrencyByLanguage,
  formatDateByLanguage,
  getClaimStatusTranslation,
  getClaimTypeTranslation
} from '../src/utils/i18n.js';

// Mock the stores
vi.mock('../src/stores/dataStore.js', () => ({
  policyDetailStore: {
    subscribe: vi.fn((callback) => {
      callback({ data: null, loading: false, error: null });
      return () => { };
    })
  },
  loadPolicyDetail: vi.fn()
}));

vi.mock('../src/stores/memberStore.js', () => ({
  selectedMemberStore: {
    subscribe: vi.fn((callback) => {
      callback(null);
      return () => { };
    })
  },
  selectedMemberDisplayName: {
    subscribe: vi.fn((callback) => {
      callback('Test Member');
      return () => { };
    })
  }
}));

vi.mock('../src/api/index.js', () => ({
  default: {
    policies: {
      searchByCitizenId: vi.fn()
    }
  }
}));

describe('PolicyDetailEN Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render no member selected state in English', () => {
    render(PolicyDetailEN);

    // Check for English text in no member selected state
    expect(screen.getByText('No Member Selected')).toBeTruthy();
    expect(screen.getByText('Please select a member from the menu above to view policy details')).toBeTruthy();
    expect(screen.getByText('View Policy List')).toBeTruthy();
    expect(screen.getByText('Back to Policy List')).toBeTruthy();
  });

  it('should use English language constant', () => {
    // Verify that the component uses English language
    expect(SUPPORTED_LANGUAGES.EN).toBe('EN');
  });
});

describe('PolicyDetail i18n Functions', () => {
  const LANGUAGE = SUPPORTED_LANGUAGES.EN;

  it('should translate PolicyDetail keys to English', () => {
    expect(tPolicyDetail('pageTitle', LANGUAGE)).toBe('Insurance Policy Details');
    expect(tPolicyDetail('backToList', LANGUAGE)).toBe('Back to Policy List');
    expect(tPolicyDetail('noMemberSelected', LANGUAGE)).toBe('No Member Selected');
    expect(tPolicyDetail('policyInfo', LANGUAGE)).toBe('Policy Information');
    expect(tPolicyDetail('mainBenefits', LANGUAGE)).toBe('Main Benefits');
    expect(tPolicyDetail('claimsHistory', LANGUAGE)).toBe('Claims History');
  });

  it('should format currency in English locale', () => {
    const amount = 1000;
    const formatted = formatCurrencyByLanguage(amount, LANGUAGE);
    expect(formatted).toMatch(/THB/); // Should contain THB currency
    expect(formatted).toMatch(/1,000/); // Should be formatted with commas
  });

  it('should format dates in English locale', () => {
    const date = '2024-06-15';
    const formatted = formatDateByLanguage(date, LANGUAGE);
    expect(formatted).toMatch(/June 15, 2024/); // English date format
  });

  it('should translate claim statuses to English', () => {
    expect(getClaimStatusTranslation('Paid', LANGUAGE)).toBe('Paid');
    expect(getClaimStatusTranslation('Approved', LANGUAGE)).toBe('Approved');
    expect(getClaimStatusTranslation('Pending', LANGUAGE)).toBe('Pending');
    expect(getClaimStatusTranslation('Rejected', LANGUAGE)).toBe('Rejected');
  });

  it('should translate claim types to English', () => {
    expect(getClaimTypeTranslation('AUTO', LANGUAGE)).toBe('Auto');
    expect(getClaimTypeTranslation('HEALTH', LANGUAGE)).toBe('Health');
    expect(getClaimTypeTranslation('LIFE', LANGUAGE)).toBe('Life');
    expect(getClaimTypeTranslation('Medical', LANGUAGE)).toBe('Medical');
  });

  it('should handle missing translations gracefully', () => {
    expect(tPolicyDetail('nonExistentKey', LANGUAGE)).toBe('nonExistentKey');
    expect(getClaimStatusTranslation('UnknownStatus', LANGUAGE)).toBe('UnknownStatus');
    expect(getClaimTypeTranslation('UnknownType', LANGUAGE)).toBe('UnknownType');
  });

  it('should handle null/undefined values gracefully', () => {
    // Currency formatting for null/NaN returns N/A
    expect(formatCurrencyByLanguage(NaN, LANGUAGE)).toBe('N/A');
    expect(formatDateByLanguage(null, LANGUAGE)).toBe('N/A');
    expect(getClaimStatusTranslation(null, LANGUAGE)).toBe('Unknown');
    expect(getClaimTypeTranslation(null, LANGUAGE)).toBe('Not specified');

    // For null values, formatCurrencyByLanguage treats it as 0
    const nullResult = formatCurrencyByLanguage(null, LANGUAGE);
    expect(nullResult).toMatch(/THB/); // Should contain THB currency
  });
});

describe('PolicyDetail Translation Coverage', () => {
  const LANGUAGE = SUPPORTED_LANGUAGES.EN;

  it('should have translations for all major sections', () => {
    // Policy information section
    expect(tPolicyDetail('policyNo', LANGUAGE)).toBe('Policy Number');
    expect(tPolicyDetail('certificateNo', LANGUAGE)).toBe('Certificate Number');
    expect(tPolicyDetail('planName', LANGUAGE)).toBe('Insurance Plan');
    expect(tPolicyDetail('effectiveFrom', LANGUAGE)).toBe('Effective From');
    expect(tPolicyDetail('effectiveTo', LANGUAGE)).toBe('Effective To');
    expect(tPolicyDetail('insurerName', LANGUAGE)).toBe('Insurance Company');
    expect(tPolicyDetail('contractCompany', LANGUAGE)).toBe('Contract Company');

    // Benefits section
    expect(tPolicyDetail('detailedBenefits', LANGUAGE)).toBe('Detailed Coverage');
    expect(tPolicyDetail('limitPerVisit', LANGUAGE)).toBe('Limit per Visit');
    expect(tPolicyDetail('limitPerYear', LANGUAGE)).toBe('Annual Limit');
    expect(tPolicyDetail('remainingPerYear', LANGUAGE)).toBe('Remaining per Year');

    // Conditions section
    expect(tPolicyDetail('contractConditions', LANGUAGE)).toBe('Contract Conditions');
    expect(tPolicyDetail('memberConditions', LANGUAGE)).toBe('Member-Specific Conditions');
    expect(tPolicyDetail('exclusion', LANGUAGE)).toBe('Exclusion');
    expect(tPolicyDetail('waitingPeriod', LANGUAGE)).toBe('Waiting Period');

    // Claims section
    expect(tPolicyDetail('claimNumber', LANGUAGE)).toBe('Claim No.');
    expect(tPolicyDetail('claimStatus', LANGUAGE)).toBe('Claim Status');
    expect(tPolicyDetail('claimType', LANGUAGE)).toBe('Claim Type');
    expect(tPolicyDetail('paidAmount', LANGUAGE)).toBe('Paid Amount');
    expect(tPolicyDetail('claimedAmount', LANGUAGE)).toBe('Claimed Amount');
    expect(tPolicyDetail('serviceDate', LANGUAGE)).toBe('Service Date');
    expect(tPolicyDetail('provider', LANGUAGE)).toBe('Healthcare Provider');
  });

  it('should have translations for error and loading states', () => {
    expect(tPolicyDetail('loadingDetails', LANGUAGE)).toBe('Loading policy details...');
    expect(tPolicyDetail('errorLoadingDetails', LANGUAGE)).toBe('Unable to load policy details. Please try again.');
    expect(tPolicyDetail('policyNotFound', LANGUAGE)).toBe('Requested policy information not found');
    expect(tPolicyDetail('noClaimsFound', LANGUAGE)).toBe('No Claims Found');
    expect(tPolicyDetail('noClaimsDescription', LANGUAGE)).toBe('No claim history found for this member, or data may not be updated yet');
  });

  it('should have translations for action buttons and alerts', () => {
    expect(tPolicyDetail('editPolicy', LANGUAGE)).toBe('Edit Policy');
    expect(tPolicyDetail('renewPolicy', LANGUAGE)).toBe('Renew Policy');
    expect(tPolicyDetail('cancelPolicy', LANGUAGE)).toBe('Cancel Policy');
    expect(tPolicyDetail('editPolicyAlert', LANGUAGE)).toBe('Policy editing functionality will be developed in the future');
    expect(tPolicyDetail('renewPolicyAlert', LANGUAGE)).toBe('Policy renewal functionality will be developed in the future');
    expect(tPolicyDetail('cancelPolicyConfirm', LANGUAGE)).toBe('Are you sure you want to cancel this policy?');
  });
});
